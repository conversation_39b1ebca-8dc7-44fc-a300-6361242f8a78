"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
function activate(context) {
    console.log('MaxPat Parser extension is now active!');
    // Register command for validating connections
    const validateCommand = vscode.commands.registerCommand('maxpat.validateConnections', () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        if (!editor.document.fileName.endsWith('.maxpat')) {
            vscode.window.showErrorMessage('Current file is not a .maxpat file');
            return;
        }
        validateMaxPatConnections(editor.document);
    });
    context.subscriptions.push(validateCommand);
    // Register document validation on save
    const onSave = vscode.workspace.onDidSaveTextDocument((document) => {
        if (document.languageId === 'maxpat') {
            validateMaxPatConnections(document);
        }
    });
    context.subscriptions.push(onSave);
    // Register hover provider for Max objects
    const hoverProvider = vscode.languages.registerHoverProvider('maxpat', {
        provideHover(document, position) {
            return provideMaxPatHover(document, position);
        }
    });
    context.subscriptions.push(hoverProvider);
    // Register completion provider for Max object names
    const completionProvider = vscode.languages.registerCompletionItemProvider('maxpat', {
        provideCompletionItems(document, position) {
            return provideMaxPatCompletions(document, position);
        }
    }, '"'); // Trigger on quote character
    context.subscriptions.push(completionProvider);
}
exports.activate = activate;
function validateMaxPatConnections(document) {
    try {
        const content = document.getText();
        const maxpat = JSON.parse(content);
        if (!maxpat.patcher) {
            vscode.window.showErrorMessage('Invalid MaxPat file: missing patcher object');
            return;
        }
        const patcher = maxpat.patcher;
        const boxes = patcher.boxes || [];
        const lines = patcher.lines || [];
        // Create a map of box IDs to box objects
        const boxMap = new Map();
        boxes.forEach((boxContainer) => {
            if (boxContainer.box && boxContainer.box.id) {
                boxMap.set(boxContainer.box.id, boxContainer.box);
            }
        });
        const errors = [];
        // Validate box structure
        validateBoxes(boxes, errors);
        // Validate connections
        validateConnections(lines, boxMap, errors);
        // Validate signal flow
        validateSignalFlow(lines, boxMap, errors);
        // Display results
        displayValidationResults(errors);
    }
    catch (error) {
        vscode.window.showErrorMessage(`Error parsing MaxPat file: ${error}`);
    }
}
function validateBoxes(boxes, errors) {
    const usedIds = new Set();
    boxes.forEach((boxContainer, index) => {
        if (!boxContainer.box) {
            errors.push({
                message: `Box ${index + 1}: Missing box object`,
                severity: 'error'
            });
            return;
        }
        const box = boxContainer.box;
        // Check for required fields
        if (!box.id) {
            errors.push({
                message: `Box ${index + 1}: Missing id`,
                severity: 'error'
            });
        }
        else {
            // Check for duplicate IDs
            if (usedIds.has(box.id)) {
                errors.push({
                    message: `Box ${index + 1}: Duplicate ID '${box.id}'`,
                    severity: 'error'
                });
            }
            usedIds.add(box.id);
        }
        if (!box.maxclass) {
            errors.push({
                message: `Box ${index + 1} (${box.id}): Missing maxclass`,
                severity: 'error'
            });
        }
        // Validate inlet/outlet counts
        if (box.numinlets !== undefined && box.numinlets < 0) {
            errors.push({
                message: `Box ${box.id}: Invalid numinlets (${box.numinlets})`,
                severity: 'error'
            });
        }
        if (box.numoutlets !== undefined && box.numoutlets < 0) {
            errors.push({
                message: `Box ${box.id}: Invalid numoutlets (${box.numoutlets})`,
                severity: 'error'
            });
        }
        // Validate outlet types match outlet count
        if (box.outlettype && box.numoutlets !== undefined) {
            if (box.outlettype.length !== box.numoutlets) {
                errors.push({
                    message: `Box ${box.id}: Outlet type count (${box.outlettype.length}) doesn't match numoutlets (${box.numoutlets})`,
                    severity: 'warning'
                });
            }
        }
    });
}
function validateConnections(lines, boxMap, errors) {
    lines.forEach((lineContainer, index) => {
        if (!lineContainer.patchline) {
            errors.push({
                message: `Line ${index + 1}: Missing patchline object`,
                severity: 'error'
            });
            return;
        }
        const line = lineContainer.patchline;
        if (!line.source || !line.destination) {
            errors.push({
                message: `Line ${index + 1}: Missing source or destination`,
                severity: 'error'
            });
            return;
        }
        const [sourceId, sourceOutlet] = line.source;
        const [destId, destInlet] = line.destination;
        // Check if source box exists
        const sourceBox = boxMap.get(sourceId);
        if (!sourceBox) {
            errors.push({
                message: `Line ${index + 1}: Source box '${sourceId}' not found`,
                severity: 'error'
            });
            return;
        }
        // Check if destination box exists
        const destBox = boxMap.get(destId);
        if (!destBox) {
            errors.push({
                message: `Line ${index + 1}: Destination box '${destId}' not found`,
                severity: 'error'
            });
            return;
        }
        // Check outlet/inlet bounds
        if (sourceBox.numoutlets !== undefined && sourceOutlet >= sourceBox.numoutlets) {
            errors.push({
                message: `Line ${index + 1}: Source outlet ${sourceOutlet} exceeds available outlets (${sourceBox.numoutlets}) for box '${sourceId}' (${sourceBox.maxclass})`,
                severity: 'error'
            });
        }
        if (destBox.numinlets !== undefined && destInlet >= destBox.numinlets) {
            errors.push({
                message: `Line ${index + 1}: Destination inlet ${destInlet} exceeds available inlets (${destBox.numinlets}) for box '${destId}' (${destBox.maxclass})`,
                severity: 'error'
            });
        }
        // Check for negative indices
        if (sourceOutlet < 0) {
            errors.push({
                message: `Line ${index + 1}: Invalid source outlet index ${sourceOutlet}`,
                severity: 'error'
            });
        }
        if (destInlet < 0) {
            errors.push({
                message: `Line ${index + 1}: Invalid destination inlet index ${destInlet}`,
                severity: 'error'
            });
        }
    });
}
function validateSignalFlow(lines, boxMap, errors) {
    // Check for signal/control flow mismatches
    lines.forEach((lineContainer, index) => {
        if (!lineContainer.patchline)
            return;
        const line = lineContainer.patchline;
        const [sourceId, sourceOutlet] = line.source;
        const [destId] = line.destination;
        const sourceBox = boxMap.get(sourceId);
        const destBox = boxMap.get(destId);
        if (!sourceBox || !destBox)
            return;
        // Check signal flow compatibility
        const sourceIsSignal = sourceBox.maxclass?.includes('~') ||
            (sourceBox.outlettype && sourceBox.outlettype[sourceOutlet] === 'signal');
        const destIsSignal = destBox.maxclass?.includes('~');
        if (sourceIsSignal && destBox.maxclass && !destIsSignal) {
            errors.push({
                message: `Line ${index + 1}: Signal output from '${sourceBox.maxclass}' connected to non-signal input of '${destBox.maxclass}'`,
                severity: 'warning'
            });
        }
        // Check for common connection mistakes
        if (sourceBox.maxclass === 'bang' && destBox.maxclass?.includes('~')) {
            errors.push({
                message: `Line ${index + 1}: Bang connected to signal object '${destBox.maxclass}' - this may not work as expected`,
                severity: 'info'
            });
        }
    });
}
function displayValidationResults(errors) {
    if (errors.length === 0) {
        vscode.window.showInformationMessage('MaxPat file is valid! All connections check out.');
        return;
    }
    const errorCount = errors.filter(e => e.severity === 'error').length;
    const warningCount = errors.filter(e => e.severity === 'warning').length;
    const infoCount = errors.filter(e => e.severity === 'info').length;
    let message = 'MaxPat validation completed:\n';
    if (errorCount > 0)
        message += `${errorCount} error(s)\n`;
    if (warningCount > 0)
        message += `${warningCount} warning(s)\n`;
    if (infoCount > 0)
        message += `${infoCount} info message(s)\n`;
    message += '\nDetails:\n' + errors.map(e => `${e.severity.toUpperCase()}: ${e.message}`).join('\n');
    if (errorCount > 0) {
        vscode.window.showErrorMessage(message);
    }
    else if (warningCount > 0) {
        vscode.window.showWarningMessage(message);
    }
    else {
        vscode.window.showInformationMessage(message);
    }
}
// Max object documentation database
const maxObjectDocs = {
    'cycle~': {
        description: 'Cosine wave oscillator. Outputs a cosine wave at the specified frequency.',
        inlets: ['frequency (float/signal)', 'phase offset (float)'],
        outlets: ['signal output']
    },
    'ezdac~': {
        description: 'Easy digital-to-analog converter. Simple audio output object.',
        inlets: ['left channel (signal)', 'right channel (signal)'],
        outlets: []
    },
    'gain~': {
        description: 'Signal gain/volume control with UI slider.',
        inlets: ['signal input'],
        outlets: ['signal output', 'gain value']
    },
    'newobj': {
        description: 'Generic object box. The actual functionality depends on the text content.',
        inlets: ['varies by object'],
        outlets: ['varies by object']
    },
    'message': {
        description: 'Message box. Stores and outputs messages when clicked or triggered.',
        inlets: ['trigger input', 'set input'],
        outlets: ['message output']
    },
    'bang': {
        description: 'Bang button. Outputs a bang message when clicked or triggered.',
        inlets: ['trigger input'],
        outlets: ['bang output']
    },
    'toggle': {
        description: 'Toggle switch. Outputs 0 or 1.',
        inlets: ['trigger input', 'set input'],
        outlets: ['0 or 1 output']
    },
    'number': {
        description: 'Number box for integer values.',
        inlets: ['number input', 'set input'],
        outlets: ['number output']
    },
    'flonum': {
        description: 'Floating point number box.',
        inlets: ['float input', 'set input'],
        outlets: ['float output']
    }
};
function provideMaxPatHover(document, position) {
    const range = document.getWordRangeAtPosition(position);
    if (!range)
        return undefined;
    const word = document.getText(range);
    // Check if we're hovering over a maxclass value
    const line = document.lineAt(position.line);
    const lineText = line.text;
    if (lineText.includes('"maxclass"') && lineText.includes(`"${word}"`)) {
        const objectDoc = maxObjectDocs[word];
        if (objectDoc) {
            const markdown = new vscode.MarkdownString();
            markdown.appendMarkdown(`**${word}**\n\n`);
            markdown.appendMarkdown(`${objectDoc.description}\n\n`);
            if (objectDoc.inlets.length > 0) {
                markdown.appendMarkdown(`**Inlets:**\n`);
                objectDoc.inlets.forEach((inlet, index) => {
                    markdown.appendMarkdown(`- ${index}: ${inlet}\n`);
                });
                markdown.appendMarkdown('\n');
            }
            if (objectDoc.outlets.length > 0) {
                markdown.appendMarkdown(`**Outlets:**\n`);
                objectDoc.outlets.forEach((outlet, index) => {
                    markdown.appendMarkdown(`- ${index}: ${outlet}\n`);
                });
            }
            return new vscode.Hover(markdown, range);
        }
    }
    return undefined;
}
function provideMaxPatCompletions(document, position) {
    const line = document.lineAt(position.line);
    const lineText = line.text;
    // Check if we're in a maxclass field
    if (lineText.includes('"maxclass"') && lineText.includes('"')) {
        const completions = [];
        Object.keys(maxObjectDocs).forEach(objectName => {
            const completion = new vscode.CompletionItem(objectName, vscode.CompletionItemKind.Class);
            completion.detail = maxObjectDocs[objectName].description;
            completion.documentation = new vscode.MarkdownString(maxObjectDocs[objectName].description);
            completions.push(completion);
        });
        return completions;
    }
    return [];
}
function deactivate() {
    // Cleanup if needed
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map