import { TextDocument, TextDocumentChangeEvent, TextDocumentWillSaveEvent, TextEdit as VTextEdit, DocumentSelector as VDocumentSelector, Event } from 'vscode';
import { ClientCapabilities, DidChangeTextDocumentParams, DidCloseTextDocumentParams, DidOpenTextDocumentParams, DidSaveTextDocumentParams, DocumentSelector, ProtocolNotificationType, RegistrationType, ServerCapabilities, TextDocumentChangeRegistrationOptions, TextDocumentRegistrationOptions, TextDocumentSaveRegistrationOptions, TextDocumentSyncKind, TextDocumentSyncOptions, WillSaveTextDocumentParams } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentEventFeature, DynamicFeature, NextSignature, TextDocumentSendFeature, NotifyingFeature, RegistrationData, DynamicDocumentFeature, NotificationSendEvent } from './features';
export interface TextDocumentSynchronizationMiddleware {
    didOpen?: NextSignature<TextDocument, Promise<void>>;
    didChange?: NextSignature<TextDocumentChangeEvent, Promise<void>>;
    willSave?: NextSignature<TextDocumentWillSaveEvent, Promise<void>>;
    willSaveWaitUntil?: NextSignature<TextDocumentWillSaveEvent, Thenable<VTextEdit[]>>;
    didSave?: NextSignature<TextDocument, Promise<void>>;
    didClose?: NextSignature<TextDocument, Promise<void>>;
}
export interface DidOpenTextDocumentFeatureShape extends DynamicFeature<TextDocumentRegistrationOptions>, TextDocumentSendFeature<(textDocument: TextDocument) => Promise<void>>, NotifyingFeature<DidOpenTextDocumentParams> {
    openDocuments: Iterable<TextDocument>;
}
export declare type ResolvedTextDocumentSyncCapabilities = {
    resolvedTextDocumentSync?: TextDocumentSyncOptions;
};
export declare class DidOpenTextDocumentFeature extends TextDocumentEventFeature<DidOpenTextDocumentParams, TextDocument, TextDocumentSynchronizationMiddleware> implements DidOpenTextDocumentFeatureShape {
    private readonly _syncedDocuments;
    constructor(client: FeatureClient<TextDocumentSynchronizationMiddleware>, syncedDocuments: Map<string, TextDocument>);
    get openDocuments(): IterableIterator<TextDocument>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    get registrationType(): RegistrationType<TextDocumentRegistrationOptions>;
    register(data: RegistrationData<TextDocumentRegistrationOptions>): void;
    protected getTextDocument(data: TextDocument): TextDocument;
    protected notificationSent(textDocument: TextDocument, type: ProtocolNotificationType<DidOpenTextDocumentParams, TextDocumentRegistrationOptions>, params: DidOpenTextDocumentParams): void;
}
export interface DidCloseTextDocumentFeatureShape extends DynamicFeature<TextDocumentRegistrationOptions>, TextDocumentSendFeature<(textDocument: TextDocument) => Promise<void>>, NotifyingFeature<DidCloseTextDocumentParams> {
}
export declare class DidCloseTextDocumentFeature extends TextDocumentEventFeature<DidCloseTextDocumentParams, TextDocument, TextDocumentSynchronizationMiddleware> implements DidCloseTextDocumentFeatureShape {
    private readonly _syncedDocuments;
    private readonly _pendingTextDocumentChanges;
    constructor(client: FeatureClient<TextDocumentSynchronizationMiddleware>, syncedDocuments: Map<string, TextDocument>, pendingTextDocumentChanges: Map<string, TextDocument>);
    get registrationType(): RegistrationType<TextDocumentRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected callback(data: TextDocument): Promise<void>;
    protected getTextDocument(data: TextDocument): TextDocument;
    protected notificationSent(textDocument: TextDocument, type: ProtocolNotificationType<DidCloseTextDocumentParams, TextDocumentRegistrationOptions>, params: DidCloseTextDocumentParams): void;
    unregister(id: string): void;
}
export interface DidChangeTextDocumentFeatureShape extends DynamicFeature<TextDocumentChangeRegistrationOptions>, TextDocumentSendFeature<(event: TextDocumentChangeEvent) => Promise<void>>, NotifyingFeature<DidChangeTextDocumentParams> {
}
export declare class DidChangeTextDocumentFeature extends DynamicDocumentFeature<TextDocumentChangeRegistrationOptions, TextDocumentSynchronizationMiddleware> implements DidChangeTextDocumentFeatureShape {
    private _listener;
    private readonly _changeData;
    private readonly _onNotificationSent;
    private readonly _onPendingChangeAdded;
    private readonly _pendingTextDocumentChanges;
    private _syncKind;
    constructor(client: FeatureClient<TextDocumentSynchronizationMiddleware>, pendingTextDocumentChanges: Map<string, TextDocument>);
    get onNotificationSent(): Event<NotificationSendEvent<DidChangeTextDocumentParams>>;
    get onPendingChangeAdded(): Event<void>;
    get syncKind(): TextDocumentSyncKind;
    get registrationType(): RegistrationType<TextDocumentChangeRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    register(data: RegistrationData<TextDocumentChangeRegistrationOptions>): void;
    getDocumentSelectors(): IterableIterator<VDocumentSelector>;
    private callback;
    notificationSent(textDocument: TextDocument, type: ProtocolNotificationType<DidChangeTextDocumentParams, TextDocumentRegistrationOptions>, params: DidChangeTextDocumentParams): void;
    unregister(id: string): void;
    dispose(): void;
    getPendingDocumentChanges(excludes: Set<string>): TextDocument[];
    getProvider(document: TextDocument): {
        send: (event: TextDocumentChangeEvent) => Promise<void>;
    } | undefined;
    private updateSyncKind;
}
export declare class WillSaveFeature extends TextDocumentEventFeature<WillSaveTextDocumentParams, TextDocumentWillSaveEvent, TextDocumentSynchronizationMiddleware> {
    constructor(client: FeatureClient<TextDocumentSynchronizationMiddleware>);
    get registrationType(): RegistrationType<TextDocumentRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected getTextDocument(data: TextDocumentWillSaveEvent): TextDocument;
}
export declare class WillSaveWaitUntilFeature extends DynamicDocumentFeature<TextDocumentRegistrationOptions, TextDocumentSynchronizationMiddleware> {
    private _listener;
    private readonly _selectors;
    constructor(client: FeatureClient<TextDocumentSynchronizationMiddleware>);
    protected getDocumentSelectors(): IterableIterator<VDocumentSelector>;
    get registrationType(): RegistrationType<TextDocumentRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    register(data: RegistrationData<TextDocumentRegistrationOptions>): void;
    private callback;
    unregister(id: string): void;
    dispose(): void;
}
export interface DidSaveTextDocumentFeatureShape extends DynamicFeature<TextDocumentRegistrationOptions>, TextDocumentSendFeature<(textDocument: TextDocument) => Promise<void>>, NotifyingFeature<DidSaveTextDocumentParams> {
}
export declare class DidSaveTextDocumentFeature extends TextDocumentEventFeature<DidSaveTextDocumentParams, TextDocument, TextDocumentSynchronizationMiddleware> implements DidSaveTextDocumentFeatureShape {
    private _includeText;
    constructor(client: FeatureClient<TextDocumentSynchronizationMiddleware>);
    get registrationType(): RegistrationType<TextDocumentSaveRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    register(data: RegistrationData<TextDocumentSaveRegistrationOptions>): void;
    protected getTextDocument(data: TextDocument): TextDocument;
}
