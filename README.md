# MaxPat Parser - VS Code Extension

A Visual Studio Code extension for parsing, validating, and editing Max/MSP `.maxpat` files with intelligent features.

## Features

### 🔍 **JSON Schema Validation**
- Comprehensive JSON schema validation for `.maxpat` and `.maxhelp` files
- Real-time syntax checking and error highlighting
- IntelliSense support for MaxPat file structure

### 🔗 **Connection Validation**
- Validates patchline connections between Max objects
- Checks for:
  - Non-existent source/destination objects
  - Invalid inlet/outlet indices
  - Signal flow compatibility warnings
  - Duplicate object IDs

### 💡 **Smart Language Features**
- **Hover Information**: Get detailed information about Max objects
- **Auto-completion**: Intelligent suggestions for Max object names
- **Syntax Highlighting**: Specialized highlighting for MaxPat JSON structure

### ⚡ **Quick Actions**
- Command: `MaxPat: Validate Connections` - Manually validate connections
- Automatic validation on file save

## Installation

### From Source
1. Clone this repository
2. Run `npm install` to install dependencies
3. Run `npm run compile` to build the extension
4. Press `F5` in VS Code to launch a new Extension Development Host window

### Package Installation
1. Run `vsce package` to create a `.vsix` file
2. Install using `code --install-extension maxpat-parser-0.1.0.vsix`

## Usage

### Basic Usage
1. Open any `.maxpat` or `.maxhelp` file in VS Code
2. The extension automatically provides:
   - JSON schema validation
   - Syntax highlighting
   - Error detection

### Connection Validation
- **Automatic**: Validation runs when you save the file
- **Manual**: Use Command Palette (`Ctrl+Shift+P`) → "MaxPat: Validate Connections"

### Hover Information
- Hover over Max object names (in `"maxclass"` fields) to see:
  - Object description
  - Inlet/outlet information
  - Usage tips

### Auto-completion
- Type `"` in a `maxclass` field to get suggestions for Max object names
- Includes descriptions for each object

## Validation Features

### Connection Validation
The extension validates:
- ✅ Object existence (source and destination)
- ✅ Inlet/outlet bounds checking
- ✅ Signal flow compatibility
- ✅ Duplicate object ID detection
- ⚠️ Signal-to-control flow warnings
- ℹ️ Common connection pattern suggestions

### Error Types
- **Errors**: Critical issues that prevent proper patch operation
- **Warnings**: Potential issues that may cause unexpected behavior
- **Info**: Suggestions for better patch design

## Supported Max Objects

The extension includes documentation for common Max objects:
- **Audio**: `cycle~`, `ezdac~`, `gain~`, `dac~`, `adc~`
- **Control**: `bang`, `toggle`, `number`, `flonum`, `message`
- **Generic**: `newobj` (with dynamic analysis)

## File Structure

```
maxpat-parser/
├── src/
│   └── extension.ts          # Main extension logic
├── schemas/
│   └── maxpat-schema.json    # JSON schema for validation
├── syntaxes/
│   └── maxpat.tmGrammar.json # Syntax highlighting rules
├── test/
│   ├── extension.test.ts     # Test suite
│   └── fixtures/             # Test MaxPat files
├── package.json              # Extension manifest
└── README.md                 # This file
```

## Development

### Building
```bash
npm install
npm run compile
```

### Testing
```bash
npm test
```

### Debugging
1. Open the project in VS Code
2. Press `F5` to launch Extension Development Host
3. Open a `.maxpat` file to test features

## Configuration

The extension uses VS Code's built-in JSON language server enhanced with MaxPat-specific features. No additional configuration is required.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Roadmap

- [ ] Support for more Max objects
- [ ] Visual patch diagram generation
- [ ] Integration with Max/MSP application
- [ ] Advanced signal flow analysis
- [ ] Patch optimization suggestions
- [ ] Support for nested subpatchers
- [ ] Export to other formats

## Acknowledgments

- Built on the Language Server Protocol
- Inspired by the Max/MSP community
- Uses the py2max project for MaxPat format understanding
