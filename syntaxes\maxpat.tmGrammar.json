{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "MaxPat", "scopeName": "source.maxpat", "fileTypes": ["maxpat", "maxhelp"], "patterns": [{"include": "source.json"}, {"name": "keyword.control.maxpat", "match": "\\b(patcher|box|patchline|boxes|lines)\\b"}, {"name": "entity.name.class.maxpat", "match": "\\b(newobj|message|comment|flonum|number|toggle|bang|inlet|outlet|send|receive|route|select|gate|switch|counter|metro|delay|pipe|timer|clocker|tempo|transport|seq|midiparse|midiformat|notein|noteout|ctlin|ctlout|pgmin|pgmout|bendin|bendout|touchin|touchout|midiin|midiout|sysexin|sysexout|cycle~|osc~|phasor~|saw~|tri~|rect~|noise~|pink~|rand~|drunk~|urn~|gain~|*~|+~|-~|/~|>~|<~|>=~|<=~|==~|!=~|abs~|sqrt~|pow~|log~|exp~|sin~|cos~|tan~|tanh~|atan~|atan2~|clip~|scale~|line~|curve~|ramp~|slide~|onepole~|biquad~|allpass~|comb~|delay~|delread~|delwrite~|vd~|tapin~|tapout~|fft~|ifft~|cartopol~|poltocar~|dac~|adc~|ezdac~|ezadc~|sfplay~|sfrecord~|groove~|buffer~|peek~|poke~|index~|lookup~|wave~|cycle~|table|coll|dict|text|textedit|umenu|radiogroup|pictslider|multislider|function|breakpoints|table|matrixctrl|pictctrl|fpic|panel|bpatcher|pcontrol|thispatcher|patcher|p|subpatch|poly~|pfft~|gen~|codebox|jit\\.matrix|jit\\.pwindow|jit\\.gl\\.render|jit\\.gl\\.videoplane|jit\\.gl\\.texture|jit\\.gl\\.mesh|jit\\.gl\\.material|jit\\.gl\\.light|jit\\.gl\\.camera|jit\\.gl\\.model|jit\\.gl\\.nurbs|jit\\.gl\\.sketch|jit\\.gl\\.gridshape|jit\\.gl\\.multiple|jit\\.gl\\.handle|jit\\.gl\\.node|jit\\.movie|jit\\.qt\\.movie|jit\\.qt\\.record|jit\\.qt\\.grab|jit\\.dx\\.grab|jit\\.grab|jit\\.broadcast|jit\\.net\\.send|jit\\.net\\.recv|jit\\.vcr|jit\\.record|jit\\.playback|jit\\.iter|jit\\.submatrix|jit\\.transpose|jit\\.reverse|jit\\.flip|jit\\.rot|jit\\.resize|jit\\.dimmap|jit\\.repos|jit\\.concat|jit\\.split|jit\\.pack|jit\\.unpack|jit\\.op|jit\\.expr|jit\\.noise|jit\\.brcosa|jit\\.hue|jit\\.saturation|jit\\.brightness|jit\\.contrast|jit\\.alphablend|jit\\.blend|jit\\.chromakey|jit\\.lumakey|jit\\.rota|jit\\.slide|jit\\.xfade|jit\\.convolve|jit\\.sobel|jit\\.edge|jit\\.emboss|jit\\.blur|jit\\.charmap|jit\\.histogram|jit\\.3m|jit\\.scanslide|jit\\.scanwrap|jit\\.wake|jit\\.bonjour|jit\\.uldl|jit\\.tcp|jit\\.udp)\\b"}, {"name": "string.quoted.double.maxpat.objectid", "match": "\"obj-\\d+\"", "captures": {"0": {"name": "entity.name.tag.maxpat"}}}, {"name": "constant.numeric.maxpat.coordinates", "match": "\\b\\d+\\.\\d+\\b(?=\\s*,\\s*\\d+\\.\\d+\\s*,\\s*\\d+\\.\\d+\\s*,\\s*\\d+\\.\\d+)"}, {"name": "keyword.other.maxpat.properties", "match": "\\b(maxclass|numinlets|numoutlets|outlettype|patching_rect|text|varname|id|source|destination|order|hidden)\\b"}]}