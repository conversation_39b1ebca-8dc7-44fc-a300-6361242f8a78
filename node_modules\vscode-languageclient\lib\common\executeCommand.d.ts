import { ProviderResult } from 'vscode';
import { ClientCapabilities, ServerCapabilities, ExecuteCommandRegistrationOptions, RegistrationType } from 'vscode-languageserver-protocol';
import { FeatureClient, DynamicFeature, FeatureState, RegistrationData } from './features';
export interface ExecuteCommandSignature {
    (this: void, command: string, args: any[]): ProviderResult<any>;
}
export interface ExecuteCommandMiddleware {
    executeCommand?: (this: void, command: string, args: any[], next: ExecuteCommandSignature) => ProviderResult<any>;
}
export declare class ExecuteCommandFeature implements DynamicFeature<ExecuteCommandRegistrationOptions> {
    private readonly _client;
    private readonly _commands;
    constructor(client: FeatureClient<ExecuteCommandMiddleware>);
    getState(): FeatureState;
    get registrationType(): RegistrationType<ExecuteCommandRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities): void;
    register(data: RegistrationData<ExecuteCommandRegistrationOptions>): void;
    unregister(id: string): void;
    dispose(): void;
}
