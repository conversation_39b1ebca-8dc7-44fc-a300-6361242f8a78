import { Disposable, CancellationToken, ProviderResult, Diagnostic as VDiagnostic, TextDocument, Event as VEvent, EventEmitter, Uri } from 'vscode';
import { ClientCapabilities, ServerCapabilities, DocumentSelector, DiagnosticRegistrationOptions, DiagnosticOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient } from './features';
export declare namespace vsdiag {
    enum DocumentDiagnosticReportKind {
        full = "full",
        unChanged = "unChanged"
    }
    interface FullDocumentDiagnosticReport {
        kind: DocumentDiagnosticReportKind.full;
        resultId?: string;
        items: VDiagnostic[];
    }
    interface RelatedFullDocumentDiagnosticReport extends FullDocumentDiagnosticReport {
        relatedDocuments?: {
            [uri: string /** DocumentUri */]: FullDocumentDiagnosticReport | UnchangedDocumentDiagnosticReport;
        };
    }
    interface UnchangedDocumentDiagnosticReport {
        kind: DocumentDiagnosticReportKind.unChanged;
        resultId: string;
    }
    interface RelatedUnchangedDocumentDiagnosticReport extends UnchangedDocumentDiagnosticReport {
        relatedDocuments?: {
            [uri: string /** DocumentUri */]: FullDocumentDiagnosticReport | UnchangedDocumentDiagnosticReport;
        };
    }
    type DocumentDiagnosticReport = RelatedFullDocumentDiagnosticReport | RelatedUnchangedDocumentDiagnosticReport;
    type PreviousResultId = {
        uri: Uri;
        value: string;
    };
    interface WorkspaceFullDocumentDiagnosticReport extends FullDocumentDiagnosticReport {
        uri: Uri;
        version: number | null;
    }
    interface WorkspaceUnchangedDocumentDiagnosticReport extends UnchangedDocumentDiagnosticReport {
        uri: Uri;
        version: number | null;
    }
    type WorkspaceDocumentDiagnosticReport = WorkspaceFullDocumentDiagnosticReport | WorkspaceUnchangedDocumentDiagnosticReport;
    interface WorkspaceDiagnosticReport {
        items: WorkspaceDocumentDiagnosticReport[];
    }
    interface WorkspaceDiagnosticReportPartialResult {
        items: WorkspaceDocumentDiagnosticReport[];
    }
    interface ResultReporter {
        (chunk: WorkspaceDiagnosticReportPartialResult | null): void;
    }
    interface DiagnosticProvider {
        onDidChangeDiagnostics: VEvent<void>;
        provideDiagnostics(document: TextDocument | Uri, previousResultId: string | undefined, token: CancellationToken): ProviderResult<DocumentDiagnosticReport>;
        provideWorkspaceDiagnostics?(resultIds: PreviousResultId[], token: CancellationToken, resultReporter: ResultReporter): ProviderResult<WorkspaceDiagnosticReport>;
    }
}
export declare type ProvideDiagnosticSignature = (this: void, document: TextDocument | Uri, previousResultId: string | undefined, token: CancellationToken) => ProviderResult<vsdiag.DocumentDiagnosticReport>;
export declare type ProvideWorkspaceDiagnosticSignature = (this: void, resultIds: vsdiag.PreviousResultId[], token: CancellationToken, resultReporter: vsdiag.ResultReporter) => ProviderResult<vsdiag.WorkspaceDiagnosticReport>;
export declare type DiagnosticProviderMiddleware = {
    provideDiagnostics?: (this: void, document: TextDocument | Uri, previousResultId: string | undefined, token: CancellationToken, next: ProvideDiagnosticSignature) => ProviderResult<vsdiag.DocumentDiagnosticReport>;
    provideWorkspaceDiagnostics?: (this: void, resultIds: vsdiag.PreviousResultId[], token: CancellationToken, resultReporter: vsdiag.ResultReporter, next: ProvideWorkspaceDiagnosticSignature) => ProviderResult<vsdiag.WorkspaceDiagnosticReport>;
};
export declare enum DiagnosticPullMode {
    onType = "onType",
    onSave = "onSave"
}
export declare type DiagnosticPullOptions = {
    /**
     * Whether to pull for diagnostics on document change.
     */
    onChange?: boolean;
    /**
     * Whether to pull for diagnostics on document save.
     */
    onSave?: boolean;
    /**
     * An optional filter method that is consulted when triggering a
     * diagnostic pull during document change or document save.
     *
     * The document gets filtered if the method returns `true`.
     *
     * @param document The document that changed or got saved.
     * @param mode The pull mode.
     * @returns whether the document should be filtered (`true`) or not.
     */
    filter?(document: TextDocument, mode: DiagnosticPullMode): boolean;
    /**
     * Whether to pull for diagnostics on resources of non instantiated
     * tabs. If it is set to true it is highly recommended to provide
     * a match method as well. Otherwise the client will not pull for
     * tabs if the used document selector specifies a language property
     * since the language value is not known for resources.
     */
    onTabs?: boolean;
    /**
     * An optional match method that is consulted when pulling for diagnostics
     * when only a URI is known (e.g. for not instantiated tabs)
     *
     * The method should return `true` if the document selector matches the
     * given resource. See also the `vscode.languages.match` function.
     *
     * @param documentSelector The document selector.
     * @param resource The resource.
     * @returns whether the resource is matched by the given document selector.
     */
    match?(documentSelector: DocumentSelector, resource: Uri): boolean;
};
export declare type $DiagnosticPullOptions = {
    diagnosticPullOptions?: DiagnosticPullOptions;
};
export declare type DiagnosticProviderShape = {
    onDidChangeDiagnosticsEmitter: EventEmitter<void>;
    diagnostics: vsdiag.DiagnosticProvider;
};
export declare class DiagnosticFeature extends TextDocumentLanguageFeature<DiagnosticOptions, DiagnosticRegistrationOptions, DiagnosticProviderShape, DiagnosticProviderMiddleware, $DiagnosticPullOptions> {
    private tabs;
    constructor(client: FeatureClient<DiagnosticProviderMiddleware, $DiagnosticPullOptions>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    dispose(): void;
    protected registerLanguageProvider(options: DiagnosticRegistrationOptions): [Disposable, DiagnosticProviderShape];
}
