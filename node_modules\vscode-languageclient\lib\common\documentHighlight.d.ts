import { TextDocument, Disposable, Position as VPosition, CancellationToken, ProviderResult, DocumentHighlightProvider, DocumentHighlight as VDocumentHighlight } from 'vscode';
import { ClientCapabilities, DocumentHighlightOptions, DocumentHighlightRegistrationOptions, DocumentSelector, ServerCapabilities, TextDocumentRegistrationOptions } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature } from './features';
export interface ProvideDocumentHighlightsSignature {
    (this: void, document: TextDocument, position: VPosition, token: CancellationToken): ProviderResult<VDocumentHighlight[]>;
}
export interface DocumentHighlightMiddleware {
    provideDocumentHighlights?: (this: void, document: TextDocument, position: VPosition, token: CancellationToken, next: ProvideDocumentHighlightsSignature) => ProviderResult<VDocumentHighlight[]>;
}
export declare class DocumentHighlightFeature extends TextDocumentLanguageFeature<boolean | DocumentHighlightOptions, DocumentHighlightRegistrationOptions, DocumentHighlightProvider, DocumentHighlightMiddleware> {
    constructor(client: FeatureClient<DocumentHighlightMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: TextDocumentRegistrationOptions): [Disposable, DocumentHighlightProvider];
}
