import { TextDocument, Disposable, Position as VPosition, CancellationToken, ProviderResult, DefinitionProvider, Definition as VDefinition, DefinitionLink as VDefinitionLink } from 'vscode';
import { ClientCapabilities, DefinitionOptions, DefinitionRegistrationOptions, DocumentSelector, ServerCapabilities } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature } from './features';
export interface ProvideDefinitionSignature {
    (this: void, document: TextDocument, position: VPosition, token: CancellationToken): ProviderResult<VDefinition | VDefinitionLink[]>;
}
export interface DefinitionMiddleware {
    provideDefinition?: (this: void, document: TextDocument, position: VPosition, token: CancellationToken, next: ProvideDefinitionSignature) => ProviderResult<VDefinition | VDefinitionLink[]>;
}
export declare class DefinitionFeature extends TextDocumentLanguageFeature<boolean | DefinitionOptions, DefinitionRegistrationOptions, DefinitionProvider, DefinitionMiddleware> {
    constructor(client: FeatureClient<DefinitionMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: DefinitionRegistrationOptions): [Disposable, DefinitionProvider];
    private registerProvider;
}
