// Simple test to verify VS Code DiagnosticCollection works
const vscode = require('vscode');

function activate(context) {
    console.log('Simple diagnostic test extension activated');
    
    // Create diagnostic collection
    const diagnosticCollection = vscode.languages.createDiagnosticCollection('test');
    
    // Create output channel
    const outputChannel = vscode.window.createOutputChannel('Diagnostic Test');
    outputChannel.show();
    outputChannel.appendLine('Diagnostic test extension activated');
    
    // Add to subscriptions
    context.subscriptions.push(diagnosticCollection, outputChannel);
    
    // Create test diagnostic immediately
    setTimeout(() => {
        outputChannel.appendLine('Creating test diagnostic...');
        
        const activeEditor = vscode.window.activeTextEditor;
        if (activeEditor) {
            outputChannel.appendLine(`Active file: ${activeEditor.document.fileName}`);
            
            const diagnostic = new vscode.Diagnostic(
                new vscode.Range(0, 0, 0, 5),
                'This is a test diagnostic to verify the system works',
                vscode.DiagnosticSeverity.Error
            );
            
            diagnosticCollection.set(activeEditor.document.uri, [diagnostic]);
            
            // Force show Problems panel
            vscode.commands.executeCommand('workbench.actions.view.problems');
            
            outputChannel.appendLine('Test diagnostic created and Problems panel opened');
        } else {
            outputChannel.appendLine('No active editor found');
        }
    }, 1000);
}

function deactivate() {}

module.exports = {
    activate,
    deactivate
};
