import { TextDocument, Disposable, Position as VPosition, CancellationToken, ProviderResult, ReferenceProvider, Location as VLocation } from 'vscode';
import { ClientCapabilities, DocumentSelector, ReferenceOptions, ReferenceRegistrationOptions, ServerCapabilities, TextDocumentRegistrationOptions } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature } from './features';
export interface ProvideReferencesSignature {
    (this: void, document: TextDocument, position: VPosition, options: {
        includeDeclaration: boolean;
    }, token: CancellationToken): ProviderResult<VLocation[]>;
}
export interface ReferencesMiddleware {
    provideReferences?: (this: void, document: TextDocument, position: VPosition, options: {
        includeDeclaration: boolean;
    }, token: CancellationToken, next: ProvideReferencesSignature) => ProviderResult<VLocation[]>;
}
export declare class ReferencesFeature extends TextDocumentLanguageFeature<boolean | ReferenceOptions, ReferenceRegistrationOptions, ReferenceProvider, ReferencesMiddleware> {
    constructor(client: FeatureClient<ReferencesMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: TextDocumentRegistrationOptions): [Disposable, ReferenceProvider];
    private registerProvider;
}
