import { Disposable, CancellationToken, ProviderResult, SymbolInformation as VSymbolInformation, WorkspaceSymbolProvider } from 'vscode';
import { ClientCapabilities, ServerCapabilities, WorkspaceSymbolRegistrationOptions } from 'vscode-languageserver-protocol';
import { FeatureClient, WorkspaceFeature } from './features';
export interface ProvideWorkspaceSymbolsSignature {
    (this: void, query: string, token: CancellationToken): ProviderResult<VSymbolInformation[]>;
}
export interface ResolveWorkspaceSymbolSignature {
    (this: void, item: VSymbolInformation, token: CancellationToken): ProviderResult<VSymbolInformation>;
}
export interface WorkspaceSymbolMiddleware {
    provideWorkspaceSymbols?: (this: void, query: string, token: CancellationToken, next: ProvideWorkspaceSymbolsSignature) => ProviderResult<VSymbolInformation[]>;
    resolveWorkspaceSymbol?: (this: void, item: VSymbolInformation, token: CancellationToken, next: ResolveWorkspaceSymbolSignature) => ProviderResult<VSymbolInformation>;
}
export declare class WorkspaceSymbolFeature extends WorkspaceFeature<WorkspaceSymbolRegistrationOptions, WorkspaceSymbolProvider, WorkspaceSymbolMiddleware> {
    constructor(client: FeatureClient<WorkspaceSymbolMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities): void;
    protected registerLanguageProvider(options: WorkspaceSymbolRegistrationOptions): [Disposable, WorkspaceSymbolProvider];
}
