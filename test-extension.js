// Simple test script to verify our extension functionality
const fs = require('fs');

// Test 1: Validate that our test files are valid JSON
console.log('Testing MaxPat Parser Extension...\n');

try {
  const validFile = fs.readFileSync('test-sample.maxpat', 'utf8');
  const validJson = JSON.parse(validFile);
  console.log('✅ test-sample.maxpat is valid JSON');
  console.log(`   - Has ${validJson.patcher.boxes.length} boxes`);
  console.log(`   - Has ${validJson.patcher.lines.length} connections`);
} catch (error) {
  console.log('❌ test-sample.maxpat failed:', error.message);
}

try {
  const invalidFile = fs.readFileSync('test-invalid.maxpat', 'utf8');
  const invalidJson = JSON.parse(invalidFile);
  console.log('✅ test-invalid.maxpat is valid JSON');
  console.log(`   - Has ${invalidJson.patcher.boxes.length} boxes`);
  console.log(`   - Has ${invalidJson.patcher.lines.length} connections (with errors)`);
} catch (error) {
  console.log('❌ test-invalid.maxpat failed:', error.message);
}

// Test 2: Check our schema file
try {
  const schema = fs.readFileSync('schemas/maxpat-schema.json', 'utf8');
  const schemaJson = JSON.parse(schema);
  console.log('✅ maxpat-schema.json is valid JSON');
  console.log(`   - Schema title: ${schemaJson.title}`);
} catch (error) {
  console.log('❌ Schema validation failed:', error.message);
}

// Test 3: Check syntax highlighting grammar
try {
  const grammar = fs.readFileSync('syntaxes/maxpat.tmGrammar.json', 'utf8');
  const grammarJson = JSON.parse(grammar);
  console.log('✅ maxpat.tmGrammar.json is valid JSON');
  console.log(`   - Grammar name: ${grammarJson.name}`);
  console.log(`   - Scope name: ${grammarJson.scopeName}`);
} catch (error) {
  console.log('❌ Grammar validation failed:', error.message);
}

console.log('\n🎉 Extension files validated successfully!');
console.log('\nTo test the extension:');
console.log('1. Open test-sample.maxpat in VS Code');
console.log('2. Use Ctrl+Shift+P and run "MaxPat: Validate Connections"');
console.log('3. Hover over "cycle~", "ezdac~", or "gain~" to see documentation');
console.log('4. Try editing a "maxclass" field and see auto-completion');
