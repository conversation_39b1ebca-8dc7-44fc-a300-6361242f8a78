"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
// Create diagnostic collection for validation errors
const diagnosticCollection = vscode.languages.createDiagnosticCollection('maxpat');
// Create output channel for debugging
const outputChannel = vscode.window.createOutputChannel('MaxPat Parser');
function activate(context) {
    console.log('MaxPat Parser extension is now active!');
    outputChannel.appendLine('MaxPat Parser extension activated');
    // Add diagnostic collection and output channel to subscriptions
    context.subscriptions.push(diagnosticCollection, outputChannel);
    // Register command for validating connections
    const validateCommand = vscode.commands.registerCommand('maxpat.validateConnections', () => {
        outputChannel.appendLine('Manual validation command triggered');
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            outputChannel.appendLine('ERROR: No active editor found');
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        outputChannel.appendLine(`Active editor file: ${editor.document.fileName}`);
        if (!editor.document.fileName.endsWith('.maxpat')) {
            outputChannel.appendLine('ERROR: Current file is not a .maxpat file');
            vscode.window.showErrorMessage('Current file is not a .maxpat file');
            return;
        }
        validateMaxPatConnections(editor.document);
    });
    context.subscriptions.push(validateCommand);
    // Register document validation on save and change
    const onSave = vscode.workspace.onDidSaveTextDocument((document) => {
        outputChannel.appendLine(`Document saved: ${document.fileName}, language: ${document.languageId}`);
        if (document.languageId === 'maxpat') {
            outputChannel.appendLine('Triggering validation on save');
            validateMaxPatConnections(document);
        }
    });
    const onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument((event) => {
        if (event.document.languageId === 'maxpat') {
            outputChannel.appendLine(`Document changed: ${event.document.fileName}`);
            // Debounce validation to avoid excessive calls
            clearTimeout(global.maxpatValidationTimeout);
            global.maxpatValidationTimeout = setTimeout(() => {
                outputChannel.appendLine('Triggering debounced validation on change');
                validateMaxPatConnections(event.document);
            }, 500);
        }
    });
    context.subscriptions.push(onSave, onDidChangeTextDocument);
    // Debug: Log when any document is opened
    const onDidOpenTextDocument = vscode.workspace.onDidOpenTextDocument((document) => {
        outputChannel.appendLine(`Document opened: ${document.fileName}, language: ${document.languageId}`);
        if (document.fileName.endsWith('.maxpat')) {
            outputChannel.appendLine('This is a .maxpat file - should trigger validation');
            if (document.languageId !== 'maxpat') {
                outputChannel.appendLine(`WARNING: Language ID is '${document.languageId}' but should be 'maxpat'`);
            }
        }
    });
    context.subscriptions.push(onDidOpenTextDocument);
    // Register hover provider for Max objects
    const hoverProvider = vscode.languages.registerHoverProvider('maxpat', {
        provideHover(document, position) {
            return provideMaxPatHover(document, position);
        }
    });
    context.subscriptions.push(hoverProvider);
    // Register completion provider for Max object names
    const completionProvider = vscode.languages.registerCompletionItemProvider('maxpat', {
        provideCompletionItems(document, position) {
            return provideMaxPatCompletions(document, position);
        }
    }, '"'); // Trigger on quote character
    context.subscriptions.push(completionProvider);
}
exports.activate = activate;
// Helper function to find line number of a specific pattern in document
function findLineNumber(document, searchPattern) {
    const text = document.getText();
    const lines = text.split('\n');
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes(searchPattern)) {
            return i + 1; // Return 1-based line number
        }
    }
    return 1; // Default to line 1 if not found
}
// Helper function to find line range for a patchline by index
function findPatchlineRange(document, patchlineIndex) {
    const text = document.getText();
    const lines = text.split('\n');
    let patchlineCount = 0;
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('"patchline"')) {
            if (patchlineCount === patchlineIndex) {
                // Find the start and end of this patchline object
                let startLine = i;
                let endLine = i;
                // Look backwards for opening brace
                for (let j = i; j >= 0; j--) {
                    if (lines[j].trim() === '{') {
                        startLine = j;
                        break;
                    }
                }
                // Look forwards for closing brace
                let braceCount = 0;
                for (let j = startLine; j < lines.length; j++) {
                    const line = lines[j];
                    braceCount += (line.match(/\{/g) || []).length;
                    braceCount -= (line.match(/\}/g) || []).length;
                    if (braceCount === 0 && j > startLine) {
                        endLine = j;
                        break;
                    }
                }
                return new vscode.Range(startLine, 0, endLine, lines[endLine].length);
            }
            patchlineCount++;
        }
    }
    return new vscode.Range(0, 0, 0, 100); // Default range
}
function validateMaxPatConnections(document) {
    outputChannel.appendLine(`Starting validation for: ${document.fileName}`);
    outputChannel.appendLine(`Document language ID: ${document.languageId}`);
    try {
        const content = document.getText();
        outputChannel.appendLine(`Document content length: ${content.length} characters`);
        const maxpat = JSON.parse(content);
        outputChannel.appendLine('JSON parsing successful');
        if (!maxpat.patcher) {
            outputChannel.appendLine('ERROR: Missing patcher object');
            const diagnostic = new vscode.Diagnostic(new vscode.Range(0, 0, 0, content.length), 'Invalid MaxPat file: missing patcher object', vscode.DiagnosticSeverity.Error);
            diagnosticCollection.set(document.uri, [diagnostic]);
            outputChannel.appendLine('Set diagnostic for missing patcher');
            return;
        }
        const patcher = maxpat.patcher;
        const boxes = patcher.boxes || [];
        const lines = patcher.lines || [];
        outputChannel.appendLine(`Found ${boxes.length} boxes and ${lines.length} lines`);
        // Create a map of box IDs to box objects
        const boxMap = new Map();
        boxes.forEach((boxContainer) => {
            if (boxContainer.box && boxContainer.box.id) {
                boxMap.set(boxContainer.box.id, boxContainer.box);
            }
        });
        outputChannel.appendLine(`Created box map with ${boxMap.size} entries`);
        const errors = [];
        // Validate box structure
        validateBoxes(document, boxes, errors);
        outputChannel.appendLine(`After box validation: ${errors.length} errors`);
        // Validate connections
        validateConnections(document, lines, boxMap, errors);
        outputChannel.appendLine(`After connection validation: ${errors.length} errors`);
        // Validate signal flow
        validateSignalFlow(document, lines, boxMap, errors);
        outputChannel.appendLine(`After signal flow validation: ${errors.length} errors`);
        // Convert errors to diagnostics and display
        displayValidationResults(document, errors);
    }
    catch (error) {
        const diagnostic = new vscode.Diagnostic(new vscode.Range(0, 0, 0, 0), `Error parsing MaxPat file: ${error}`, vscode.DiagnosticSeverity.Error);
        diagnosticCollection.set(document.uri, [diagnostic]);
    }
}
function validateBoxes(document, boxes, errors) {
    const usedIds = new Set();
    boxes.forEach((boxContainer, index) => {
        if (!boxContainer.box) {
            errors.push({
                message: `Box ${index + 1}: Missing box object`,
                severity: 'error'
            });
            return;
        }
        const box = boxContainer.box;
        // Check for required fields
        if (!box.id) {
            errors.push({
                message: `Box ${index + 1}: Missing id`,
                severity: 'error'
            });
        }
        else {
            // Check for duplicate IDs
            if (usedIds.has(box.id)) {
                errors.push({
                    message: `Box ${index + 1}: Duplicate ID '${box.id}'`,
                    severity: 'error'
                });
            }
            usedIds.add(box.id);
        }
        if (!box.maxclass) {
            errors.push({
                message: `Box ${index + 1} (${box.id}): Missing maxclass`,
                severity: 'error'
            });
        }
        // Validate inlet/outlet counts
        if (box.numinlets !== undefined && box.numinlets < 0) {
            errors.push({
                message: `Box ${box.id}: Invalid numinlets (${box.numinlets})`,
                severity: 'error'
            });
        }
        if (box.numoutlets !== undefined && box.numoutlets < 0) {
            errors.push({
                message: `Box ${box.id}: Invalid numoutlets (${box.numoutlets})`,
                severity: 'error'
            });
        }
        // Validate outlet types match outlet count
        if (box.outlettype && box.numoutlets !== undefined) {
            if (box.outlettype.length !== box.numoutlets) {
                errors.push({
                    message: `Box ${box.id}: Outlet type count (${box.outlettype.length}) doesn't match numoutlets (${box.numoutlets})`,
                    severity: 'warning'
                });
            }
        }
    });
}
function validateConnections(document, lines, boxMap, errors) {
    lines.forEach((lineContainer, index) => {
        const range = findPatchlineRange(document, index);
        if (!lineContainer.patchline) {
            errors.push({
                message: `Line ${index + 1}: Missing patchline object`,
                severity: 'error',
                range: range
            });
            return;
        }
        const line = lineContainer.patchline;
        if (!line.source || !line.destination) {
            errors.push({
                message: `Line ${index + 1}: Missing source or destination`,
                severity: 'error'
            });
            return;
        }
        const [sourceId, sourceOutlet] = line.source;
        const [destId, destInlet] = line.destination;
        // Check if source box exists
        const sourceBox = boxMap.get(sourceId);
        if (!sourceBox) {
            errors.push({
                message: `Line ${index + 1}: Source box '${sourceId}' not found`,
                severity: 'error',
                range: range
            });
            return;
        }
        // Check if destination box exists
        const destBox = boxMap.get(destId);
        if (!destBox) {
            errors.push({
                message: `Line ${index + 1}: Destination box '${destId}' not found`,
                severity: 'error',
                range: range
            });
            return;
        }
        // Check outlet/inlet bounds
        if (sourceBox.numoutlets !== undefined && sourceOutlet >= sourceBox.numoutlets) {
            errors.push({
                message: `Line ${index + 1}: Source outlet ${sourceOutlet} exceeds available outlets (${sourceBox.numoutlets}) for box '${sourceId}' (${sourceBox.maxclass})`,
                severity: 'error',
                range: range
            });
        }
        if (destBox.numinlets !== undefined && destInlet >= destBox.numinlets) {
            errors.push({
                message: `Line ${index + 1}: Destination inlet ${destInlet} exceeds available inlets (${destBox.numinlets}) for box '${destId}' (${destBox.maxclass})`,
                severity: 'error',
                range: range
            });
        }
        // Check for negative indices
        if (sourceOutlet < 0) {
            errors.push({
                message: `Line ${index + 1}: Invalid source outlet index ${sourceOutlet}`,
                severity: 'error'
            });
        }
        if (destInlet < 0) {
            errors.push({
                message: `Line ${index + 1}: Invalid destination inlet index ${destInlet}`,
                severity: 'error'
            });
        }
    });
}
function validateSignalFlow(document, lines, boxMap, errors) {
    // Check for signal/control flow mismatches
    lines.forEach((lineContainer, index) => {
        if (!lineContainer.patchline)
            return;
        const line = lineContainer.patchline;
        const [sourceId, sourceOutlet] = line.source;
        const [destId] = line.destination;
        const sourceBox = boxMap.get(sourceId);
        const destBox = boxMap.get(destId);
        if (!sourceBox || !destBox)
            return;
        // Check signal flow compatibility
        const sourceIsSignal = sourceBox.maxclass?.includes('~') ||
            (sourceBox.outlettype && sourceBox.outlettype[sourceOutlet] === 'signal');
        const destIsSignal = destBox.maxclass?.includes('~');
        if (sourceIsSignal && destBox.maxclass && !destIsSignal) {
            errors.push({
                message: `Line ${index + 1}: Signal output from '${sourceBox.maxclass}' connected to non-signal input of '${destBox.maxclass}'`,
                severity: 'warning'
            });
        }
        // Check for common connection mistakes
        if (sourceBox.maxclass === 'bang' && destBox.maxclass?.includes('~')) {
            errors.push({
                message: `Line ${index + 1}: Bang connected to signal object '${destBox.maxclass}' - this may not work as expected`,
                severity: 'info'
            });
        }
    });
}
function displayValidationResults(document, errors) {
    outputChannel.appendLine(`Displaying validation results: ${errors.length} errors found`);
    // Convert validation errors to VS Code diagnostics
    const diagnostics = errors.map((error, index) => {
        const severity = error.severity === 'error' ? vscode.DiagnosticSeverity.Error :
            error.severity === 'warning' ? vscode.DiagnosticSeverity.Warning :
                vscode.DiagnosticSeverity.Information;
        // Use provided range or create a default range
        const range = error.range || new vscode.Range(error.line ? error.line - 1 : 0, 0, error.line ? error.line - 1 : 0, 100);
        outputChannel.appendLine(`  Error ${index + 1}: ${error.message} (severity: ${error.severity})`);
        outputChannel.appendLine(`    Range: line ${range.start.line + 1}, char ${range.start.character} to line ${range.end.line + 1}, char ${range.end.character}`);
        return new vscode.Diagnostic(range, error.message, severity);
    });
    outputChannel.appendLine(`Created ${diagnostics.length} diagnostics`);
    // Set diagnostics for this document
    diagnosticCollection.set(document.uri, diagnostics);
    outputChannel.appendLine(`Set diagnostics for document: ${document.uri.toString()}`);
    // Also show a summary message for manual validation
    if (errors.length === 0) {
        vscode.window.showInformationMessage('MaxPat file is valid! All connections check out.');
    }
    else {
        const errorCount = errors.filter(e => e.severity === 'error').length;
        const warningCount = errors.filter(e => e.severity === 'warning').length;
        const infoCount = errors.filter(e => e.severity === 'info').length;
        let message = 'MaxPat validation completed:\n';
        if (errorCount > 0)
            message += `${errorCount} error(s)\n`;
        if (warningCount > 0)
            message += `${warningCount} warning(s)\n`;
        if (infoCount > 0)
            message += `${infoCount} info message(s)\n`;
        message += '\nDetails:\n' + errors.map(e => `${e.severity.toUpperCase()}: ${e.message}`).join('\n');
        if (errorCount > 0) {
            vscode.window.showErrorMessage(message);
        }
        else if (warningCount > 0) {
            vscode.window.showWarningMessage(message);
        }
        else {
            vscode.window.showInformationMessage(message);
        }
    }
}
// Max object documentation database
const maxObjectDocs = {
    'cycle~': {
        description: 'Cosine wave oscillator. Outputs a cosine wave at the specified frequency.',
        inlets: ['frequency (float/signal)', 'phase offset (float)'],
        outlets: ['signal output']
    },
    'ezdac~': {
        description: 'Easy digital-to-analog converter. Simple audio output object.',
        inlets: ['left channel (signal)', 'right channel (signal)'],
        outlets: []
    },
    'gain~': {
        description: 'Signal gain/volume control with UI slider.',
        inlets: ['signal input'],
        outlets: ['signal output', 'gain value']
    },
    'newobj': {
        description: 'Generic object box. The actual functionality depends on the text content.',
        inlets: ['varies by object'],
        outlets: ['varies by object']
    },
    'message': {
        description: 'Message box. Stores and outputs messages when clicked or triggered.',
        inlets: ['trigger input', 'set input'],
        outlets: ['message output']
    },
    'bang': {
        description: 'Bang button. Outputs a bang message when clicked or triggered.',
        inlets: ['trigger input'],
        outlets: ['bang output']
    },
    'toggle': {
        description: 'Toggle switch. Outputs 0 or 1.',
        inlets: ['trigger input', 'set input'],
        outlets: ['0 or 1 output']
    },
    'number': {
        description: 'Number box for integer values.',
        inlets: ['number input', 'set input'],
        outlets: ['number output']
    },
    'flonum': {
        description: 'Floating point number box.',
        inlets: ['float input', 'set input'],
        outlets: ['float output']
    }
};
function provideMaxPatHover(document, position) {
    const line = document.lineAt(position.line);
    const lineText = line.text;
    // Check if we're on a line with maxclass
    if (!lineText.includes('"maxclass"'))
        return undefined;
    // Find the maxclass value on this line using regex
    const maxclassMatch = lineText.match(/"maxclass":\s*"([^"]+)"/);
    if (!maxclassMatch)
        return undefined;
    const objectName = maxclassMatch[1];
    // Check if the cursor is within the maxclass value
    const valueStart = lineText.indexOf(`"${objectName}"`, lineText.indexOf('"maxclass"'));
    const valueEnd = valueStart + objectName.length + 2; // +2 for quotes
    if (position.character >= valueStart && position.character <= valueEnd) {
        const objectDoc = maxObjectDocs[objectName];
        if (objectDoc) {
            const markdown = new vscode.MarkdownString();
            markdown.appendMarkdown(`**${objectName}**\n\n`);
            markdown.appendMarkdown(`${objectDoc.description}\n\n`);
            if (objectDoc.inlets.length > 0) {
                markdown.appendMarkdown(`**Inlets:**\n`);
                objectDoc.inlets.forEach((inlet, index) => {
                    markdown.appendMarkdown(`- ${index}: ${inlet}\n`);
                });
                markdown.appendMarkdown('\n');
            }
            if (objectDoc.outlets.length > 0) {
                markdown.appendMarkdown(`**Outlets:**\n`);
                objectDoc.outlets.forEach((outlet, index) => {
                    markdown.appendMarkdown(`- ${index}: ${outlet}\n`);
                });
            }
            // Create a range for the object name
            const hoverRange = new vscode.Range(position.line, valueStart, position.line, valueEnd);
            return new vscode.Hover(markdown, hoverRange);
        }
    }
    return undefined;
}
function provideMaxPatCompletions(document, position) {
    const line = document.lineAt(position.line);
    const lineText = line.text;
    // Check if we're in a maxclass field
    if (lineText.includes('"maxclass"') && lineText.includes('"')) {
        const completions = [];
        Object.keys(maxObjectDocs).forEach(objectName => {
            const completion = new vscode.CompletionItem(objectName, vscode.CompletionItemKind.Class);
            completion.detail = maxObjectDocs[objectName].description;
            completion.documentation = new vscode.MarkdownString(maxObjectDocs[objectName].description);
            completions.push(completion);
        });
        return completions;
    }
    return [];
}
function deactivate() {
    // Cleanup if needed
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map