import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';

suite('MaxPat Parser Extension Test Suite', () => {
  vscode.window.showInformationMessage('Start all tests.');

  test('Extension should be present', () => {
    assert.ok(vscode.extensions.getExtension('maxpat-parser'));
  });

  test('Should activate extension', async () => {
    const extension = vscode.extensions.getExtension('maxpat-parser');
    if (extension) {
      await extension.activate();
      assert.ok(extension.isActive);
    }
  });

  test('Should register maxpat language', () => {
    const languages = vscode.languages.getLanguages();
    return languages.then((langs) => {
      assert.ok(langs.includes('maxpat'));
    });
  });

  test('Should validate valid maxpat file', async () => {
    const validMaxPat = {
      "patcher": {
        "fileversion": 1,
        "boxes": [
          {
            "box": {
              "id": "obj-1",
              "maxclass": "cycle~",
              "numinlets": 2,
              "numoutlets": 1,
              "outlettype": ["signal"],
              "patching_rect": [100, 100, 50, 22]
            }
          },
          {
            "box": {
              "id": "obj-2",
              "maxclass": "ezdac~",
              "numinlets": 2,
              "numoutlets": 0,
              "patching_rect": [100, 150, 45, 45]
            }
          }
        ],
        "lines": [
          {
            "patchline": {
              "source": ["obj-1", 0],
              "destination": ["obj-2", 0]
            }
          }
        ]
      }
    };

    // This would test the validation logic
    // In a real test, you'd create a temporary document and validate it
    assert.ok(validMaxPat.patcher.boxes.length === 2);
    assert.ok(validMaxPat.patcher.lines.length === 1);
  });

  test('Should detect invalid connections', async () => {
    const invalidMaxPat = {
      "patcher": {
        "fileversion": 1,
        "boxes": [
          {
            "box": {
              "id": "obj-1",
              "maxclass": "cycle~",
              "numinlets": 2,
              "numoutlets": 1,
              "outlettype": ["signal"],
              "patching_rect": [100, 100, 50, 22]
            }
          }
        ],
        "lines": [
          {
            "patchline": {
              "source": ["obj-1", 5], // Invalid outlet index
              "destination": ["obj-99", 0] // Non-existent object
            }
          }
        ]
      }
    };

    // This would test that validation catches the errors
    assert.ok(invalidMaxPat.patcher.lines[0].patchline.source[1] === 5); // Invalid outlet
    assert.ok(invalidMaxPat.patcher.lines[0].patchline.destination[0] === "obj-99"); // Invalid object
  });

  test('Should provide hover information for Max objects', () => {
    // Test that hover provider returns information for known objects
    const knownObjects = ['cycle~', 'ezdac~', 'gain~', 'bang', 'toggle'];
    knownObjects.forEach(obj => {
      // In a real test, you'd simulate hover over the object name
      assert.ok(obj.length > 0);
    });
  });

  test('Should provide completions for Max objects', () => {
    // Test that completion provider suggests Max object names
    const expectedCompletions = ['cycle~', 'ezdac~', 'gain~', 'newobj', 'message'];
    expectedCompletions.forEach(completion => {
      assert.ok(completion.length > 0);
    });
  });
});
