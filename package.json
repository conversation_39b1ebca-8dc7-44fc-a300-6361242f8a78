{"name": "maxpat-parser", "displayName": "MaxPat Parser", "description": "VS Code extension for parsing and validating Max/MSP .maxpat files", "version": "0.2.0", "publisher": "DrewIsAFK", "engines": {"vscode": "^1.74.0"}, "categories": ["Programming Languages", "Linters"], "keywords": ["maxpat", "max", "msp", "cycling74", "json", "parser", "validation"], "main": "./out/extension.js", "activationEvents": ["workspaceContains:**/*.maxpat"], "contributes": {"languages": [{"id": "maxpat", "aliases": ["MaxPat", "maxpat"], "extensions": [".maxpat", ".maxhelp"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "maxpat", "scopeName": "source.maxpat", "path": "./syntaxes/maxpat.tmGrammar.json"}], "jsonValidation": [{"fileMatch": "*.maxpat", "url": "./schemas/maxpat-schema.json"}, {"fileMatch": "*.maxhelp", "url": "./schemas/maxpat-schema.json"}], "commands": [{"command": "maxpat.validateConnections", "title": "Validate MaxPat Connections"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.74.0", "typescript": "^4.9.4"}, "dependencies": {"vscode-languageclient": "^8.1.0"}}