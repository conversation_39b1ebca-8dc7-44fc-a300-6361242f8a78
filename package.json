{"name": "maxpat-parser", "displayName": "MaxPat Parser", "description": "VS Code extension for parsing and validating Max/MSP .maxpat files", "version": "0.1.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Programming Languages", "Linters"], "keywords": ["maxpat", "max", "msp", "cycling74", "json", "parser", "validation"], "main": "./out/extension.js", "contributes": {"languages": [{"id": "maxpat", "aliases": ["MaxPat", "maxpat"], "extensions": [".maxpat", ".maxhelp"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "maxpat", "scopeName": "source.maxpat", "path": "./syntaxes/maxpat.tmGrammar.json"}], "jsonValidation": [{"fileMatch": "*.maxpat", "url": "./schemas/maxpat-schema.json"}, {"fileMatch": "*.maxhelp", "url": "./schemas/maxpat-schema.json"}], "commands": [{"command": "maxpat.validateConnections", "title": "Validate MaxPat Connections"}]}, "activationEvents": ["onLanguage:maxpat"], "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"vscode-languageclient": "^8.1.0"}}