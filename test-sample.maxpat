{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [91.0, 106.0, 640.0, 480.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"id": "obj-3", "maxclass": "gain~", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", ""], "patching_rect": [200.0, 200.0, 50.0, 50.0]}}, {"box": {"id": "obj-2", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [284.0, 272.0, 45.0, 45.0]}}, {"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [279.0, 149.0, 66.0, 22.0], "text": "cycle~ 440", "varname": "osc1"}}], "lines": [{"patchline": {"destination": ["obj-3", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-2", 1], "order": 0, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-2", 0], "order": 1, "source": ["obj-3", 0]}}], "dependency_cache": [], "autosave": 0}}