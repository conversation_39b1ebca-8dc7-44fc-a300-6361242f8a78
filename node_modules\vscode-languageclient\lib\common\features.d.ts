import { TextDocument, Event as VEvent, DocumentSelector as VDocumentSelector, Event, Disposable, CancellationToken, ProviderResult, TextEdit as VTextEdit, ReferenceProvider, DefinitionProvider, SignatureHelpProvider, HoverProvider, CompletionItemProvider, WorkspaceSymbolProvider, DocumentHighlightProvider, CodeActionProvider, DocumentFormattingEditProvider, DocumentRangeFormattingEditProvider, OnTypeFormattingEditProvider, RenameProvider, DocumentSymbolProvider, DocumentLinkProvider, DocumentColorProvider, DeclarationProvider, FoldingRangeProvider, ImplementationProvider, SelectionRangeProvider, TypeDefinitionProvider, CallHierarchyProvider, LinkedEditingRangeProvider, TypeHierarchyProvider, FileCreateEvent, FileRenameEvent, FileDeleteEvent, FileWillCreateEvent, FileWillRenameEvent, FileWillDeleteEvent, CancellationError } from 'vscode';
import { CallHierarchyPrepareRequest, ClientCapabilities, CodeActionRequest, CodeLensRequest, CompletionRequest, DeclarationRequest, DefinitionRequest, DidChangeTextDocumentNotification, DidCloseTextDocumentNotification, DidCreateFilesNotification, DidDeleteFilesNotification, DidOpenTextDocumentNotification, DidRenameFilesNotification, DidSaveTextDocumentNotification, DocumentColorRequest, DocumentDiagnosticRequest, DocumentFormattingRequest, DocumentHighlightRequest, DocumentLinkRequest, DocumentOnTypeFormattingRequest, DocumentRangeFormattingRequest, DocumentSelector, DocumentSymbolRequest, FileOperationRegistrationOptions, FoldingRangeRequest, GenericNotificationHandler, GenericRequestHandler, HoverRequest, ImplementationRequest, InitializeParams, InlayHintRequest, InlineValueRequest, LinkedEditingRangeRequest, MessageSignature, NotebookDocumentSyncRegistrationOptions, NotebookDocumentSyncRegistrationType, NotificationHandler, NotificationHandler0, NotificationType, NotificationType0, ProgressType, ProtocolNotificationType, ProtocolNotificationType0, ProtocolRequestType, ProtocolRequestType0, ReferencesRequest, RegistrationType, RenameRequest, RequestHandler, RequestHandler0, RequestType, RequestType0, SelectionRangeRequest, SemanticTokensRegistrationType, ServerCapabilities, SignatureHelpRequest, StaticRegistrationOptions, TextDocumentIdentifier, TextDocumentRegistrationOptions, TypeDefinitionRequest, TypeHierarchyPrepareRequest, WillCreateFilesRequest, WillDeleteFilesRequest, WillRenameFilesRequest, WillSaveTextDocumentNotification, WillSaveTextDocumentWaitUntilRequest, WorkspaceSymbolRequest } from 'vscode-languageserver-protocol';
import type * as c2p from './codeConverter';
import type * as p2c from './protocolConverter';
export declare class LSPCancellationError extends CancellationError {
    readonly data: object | Object;
    constructor(data: object | Object);
}
export declare function ensure<T, K extends keyof T>(target: T, key: K): T[K];
export interface NextSignature<P, R> {
    (this: void, data: P, next: (data: P) => R): R;
}
export interface RegistrationData<T> {
    id: string;
    registerOptions: T;
}
export declare type FeatureStateKind = 'document' | 'workspace' | 'static' | 'window';
export declare type FeatureState = {
    kind: 'document';
    /**
     * The features's id. This is usually the method names used during
     * registration.
     */
    id: string;
    /**
     * Has active registrations.
     */
    registrations: boolean;
    /**
     * A registration matches an open document.
     */
    matches: boolean;
} | {
    kind: 'workspace';
    /**
     * The features's id. This is usually the method names used during
     * registration.
     */
    id: string;
    /**
     * Has active registrations.
     */
    registrations: boolean;
} | {
    kind: 'window';
    /**
     * The features's id. This is usually the method names used during
     * registration.
     */
    id: string;
    /**
     * Has active registrations.
     */
    registrations: boolean;
} | {
    kind: 'static';
};
/**
 * A static feature. A static feature can't be dynamically activated via the
 * server. It is wired during the initialize sequence.
 */
export interface StaticFeature {
    /**
     * Called to fill the initialize params.
     *
     * @params the initialize params.
     */
    fillInitializeParams?: (params: InitializeParams) => void;
    /**
     * Called to fill in the client capabilities this feature implements.
     *
     * @param capabilities The client capabilities to fill.
     */
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    /**
     * A preflight where the server capabilities are shown to all features
     * before a feature is actually initialized. This allows feature to
     * capture some state if they are a pre-requisite for other features.
     *
     * @param capabilities the server capabilities
     * @param documentSelector the document selector pass to the client's constructor.
     *  May be `undefined` if the client was created without a selector.
     */
    preInitialize?: (capabilities: ServerCapabilities, documentSelector: DocumentSelector | undefined) => void;
    /**
     * Initialize the feature. This method is called on a feature instance
     * when the client has successfully received the initialize request from
     * the server and before the client sends the initialized notification
     * to the server.
     *
     * @param capabilities the server capabilities
     * @param documentSelector the document selector pass to the client's constructor.
     *  May be `undefined` if the client was created without a selector.
     */
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector | undefined): void;
    /**
     * Returns the state the feature is in.
     */
    getState(): FeatureState;
    /**
     * Called when the client is stopped to dispose this feature. Usually a feature
     * un-registers listeners registered hooked up with the VS Code extension host.
     */
    dispose(): void;
}
export declare namespace StaticFeature {
    function is(value: any): value is StaticFeature;
}
/**
 * A dynamic feature can be activated via the server.
 */
export interface DynamicFeature<RO> {
    /**
     * Called to fill the initialize params.
     *
     * @params the initialize params.
     */
    fillInitializeParams?: (params: InitializeParams) => void;
    /**
     * Called to fill in the client capabilities this feature implements.
     *
     * @param capabilities The client capabilities to fill.
     */
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    /**
     * A preflight where the server capabilities are shown to all features
     * before a feature is actually initialized. This allows feature to
     * capture some state if they are a pre-requisite for other features.
     *
     * @param capabilities the server capabilities
     * @param documentSelector the document selector pass to the client's constructor.
     *  May be `undefined` if the client was created without a selector.
     */
    preInitialize?: (capabilities: ServerCapabilities, documentSelector: DocumentSelector | undefined) => void;
    /**
     * Initialize the feature. This method is called on a feature instance
     * when the client has successfully received the initialize request from
     * the server and before the client sends the initialized notification
     * to the server.
     *
     * @param capabilities the server capabilities.
     * @param documentSelector the document selector pass to the client's constructor.
     *  May be `undefined` if the client was created without a selector.
     */
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector | undefined): void;
    /**
     * Returns the state the feature is in.
     */
    getState(): FeatureState;
    /**
     * The signature (e.g. method) for which this features support dynamic activation / registration.
     */
    registrationType: RegistrationType<RO>;
    /**
     * Is called when the server send a register request for the given message.
     *
     * @param data additional registration data as defined in the protocol.
     */
    register(data: RegistrationData<RO>): void;
    /**
     * Is called when the server wants to unregister a feature.
     *
     * @param id the id used when registering the feature.
     */
    unregister(id: string): void;
    /**
     * Called when the client is stopped to dispose this feature. Usually a feature
     * un-registers listeners registered hooked up with the VS Code extension host.
     */
    dispose(): void;
}
export declare namespace DynamicFeature {
    function is<T>(value: any): value is DynamicFeature<T>;
}
interface CreateParamsSignature<E, P> {
    (data: E): P;
}
export interface NotificationSendEvent<P extends {
    textDocument: TextDocumentIdentifier;
}> {
    textDocument: TextDocument;
    type: ProtocolNotificationType<P, TextDocumentRegistrationOptions>;
    params: P;
}
export interface NotifyingFeature<P extends {
    textDocument: TextDocumentIdentifier;
}> {
    onNotificationSent: VEvent<NotificationSendEvent<P>>;
}
/**
 * An abstract dynamic feature implementation that operates on documents (e.g. text
 * documents or notebooks).
 */
export declare abstract class DynamicDocumentFeature<RO, MW, CO = object> implements DynamicFeature<RO> {
    protected readonly _client: FeatureClient<MW, CO>;
    constructor(client: FeatureClient<MW, CO>);
    abstract fillClientCapabilities(capabilities: ClientCapabilities): void;
    abstract initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector | undefined): void;
    abstract registrationType: RegistrationType<RO>;
    abstract register(data: RegistrationData<RO>): void;
    abstract unregister(id: string): void;
    abstract dispose(): void;
    /**
     * Returns the state the feature is in.
     */
    getState(): FeatureState;
    protected abstract getDocumentSelectors(): IterableIterator<VDocumentSelector>;
}
/**
 * A mixin type that allows to send notification or requests using a registered
 * provider.
 */
export interface TextDocumentSendFeature<T extends Function> {
    /**
     * Returns a provider for the given text document.
     */
    getProvider(document: TextDocument): {
        send: T;
    } | undefined;
}
/**
 * An abstract base class to implement features that react to events
 * emitted from text documents.
 */
export declare abstract class TextDocumentEventFeature<P extends {
    textDocument: TextDocumentIdentifier;
}, E, M> extends DynamicDocumentFeature<TextDocumentRegistrationOptions, M> implements TextDocumentSendFeature<(data: E) => Promise<void>>, NotifyingFeature<P> {
    private readonly _event;
    protected readonly _type: ProtocolNotificationType<P, TextDocumentRegistrationOptions>;
    protected readonly _middleware: () => NextSignature<E, Promise<void>> | undefined;
    protected readonly _createParams: CreateParamsSignature<E, P>;
    protected readonly _textDocument: (data: E) => TextDocument;
    protected readonly _selectorFilter?: (selectors: IterableIterator<VDocumentSelector>, data: E) => boolean;
    private _listener;
    protected readonly _selectors: Map<string, VDocumentSelector>;
    private readonly _onNotificationSent;
    static textDocumentFilter(selectors: IterableIterator<VDocumentSelector>, textDocument: TextDocument): boolean;
    constructor(client: FeatureClient<M>, event: Event<E>, type: ProtocolNotificationType<P, TextDocumentRegistrationOptions>, middleware: () => NextSignature<E, Promise<void>> | undefined, createParams: CreateParamsSignature<E, P>, textDocument: (data: E) => TextDocument, selectorFilter?: (selectors: IterableIterator<VDocumentSelector>, data: E) => boolean);
    protected getStateInfo(): [IterableIterator<VDocumentSelector>, boolean];
    protected getDocumentSelectors(): IterableIterator<VDocumentSelector>;
    register(data: RegistrationData<TextDocumentRegistrationOptions>): void;
    protected callback(data: E): Promise<void>;
    private matches;
    get onNotificationSent(): VEvent<NotificationSendEvent<P>>;
    protected notificationSent(textDocument: TextDocument, type: ProtocolNotificationType<P, TextDocumentRegistrationOptions>, params: P): void;
    protected abstract getTextDocument(data: E): TextDocument;
    unregister(id: string): void;
    dispose(): void;
    getProvider(document: TextDocument): {
        send: (data: E) => Promise<void>;
    } | undefined;
}
/**
 * A mixin type to access a provider that is registered for a
 * given text document / document selector.
 */
export interface TextDocumentProviderFeature<T> {
    /**
     * Triggers the corresponding RPC method.
     */
    getProvider(textDocument: TextDocument): T | undefined;
}
export declare type DocumentSelectorOptions = {
    documentSelector: DocumentSelector;
};
/**
 * A abstract feature implementation that registers language providers
 * for text documents using a given document selector.
 */
export declare abstract class TextDocumentLanguageFeature<PO, RO extends TextDocumentRegistrationOptions & PO, PR, MW, CO = object> extends DynamicDocumentFeature<RO, MW, CO> {
    private readonly _registrationType;
    private readonly _registrations;
    constructor(client: FeatureClient<MW, CO>, registrationType: RegistrationType<RO>);
    protected getDocumentSelectors(): IterableIterator<VDocumentSelector>;
    get registrationType(): RegistrationType<RO>;
    abstract fillClientCapabilities(capabilities: ClientCapabilities): void;
    abstract initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    register(data: RegistrationData<RO>): void;
    protected abstract registerLanguageProvider(options: RO, id: string): [Disposable, PR];
    unregister(id: string): void;
    dispose(): void;
    protected getRegistration(documentSelector: DocumentSelector | undefined, capability: undefined | PO | (RO & StaticRegistrationOptions)): [string | undefined, (RO & {
        documentSelector: DocumentSelector;
    }) | undefined];
    protected getRegistrationOptions(documentSelector: DocumentSelector | undefined, capability: undefined | PO): (RO & {
        documentSelector: DocumentSelector;
    }) | undefined;
    getProvider(textDocument: TextDocument): PR | undefined;
    protected getAllProviders(): Iterable<PR>;
}
export interface WorkspaceProviderFeature<PR> {
    getProviders(): PR[] | undefined;
}
declare type WorkspaceFeatureRegistration<PR> = {
    disposable: Disposable;
    provider: PR;
};
export declare abstract class WorkspaceFeature<RO, PR, M> implements DynamicFeature<RO> {
    protected readonly _client: FeatureClient<M>;
    private readonly _registrationType;
    protected readonly _registrations: Map<string, WorkspaceFeatureRegistration<PR>>;
    constructor(client: FeatureClient<M>, registrationType: RegistrationType<RO>);
    getState(): FeatureState;
    get registrationType(): RegistrationType<RO>;
    abstract fillClientCapabilities(capabilities: ClientCapabilities): void;
    abstract initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector | undefined): void;
    register(data: RegistrationData<RO>): void;
    protected abstract registerLanguageProvider(options: RO): [Disposable, PR];
    unregister(id: string): void;
    dispose(): void;
    getProviders(): PR[];
}
export interface DedicatedTextSynchronizationFeature {
    handles(textDocument: TextDocument): boolean;
}
import type { SemanticTokensProviderShape } from './semanticTokens';
import type { DidChangeTextDocumentFeatureShape, DidCloseTextDocumentFeatureShape, DidOpenTextDocumentFeatureShape, DidSaveTextDocumentFeatureShape } from './textSynchronization';
import type { CodeLensProviderShape } from './codeLens';
import type { InlineValueProviderShape } from './inlineValue';
import type { InlayHintsProviderShape } from './inlayHint';
import type { DiagnosticProviderShape } from './diagnostic';
import type { NotebookDocumentProviderShape } from './notebook';
export interface FeatureClient<M, CO = object> {
    protocol2CodeConverter: p2c.Converter;
    code2ProtocolConverter: c2p.Converter;
    clientOptions: CO;
    middleware: M;
    start(): Promise<void>;
    isRunning(): boolean;
    stop(): Promise<void>;
    sendRequest<R, PR, E, RO>(type: ProtocolRequestType0<R, PR, E, RO>, token?: CancellationToken): Promise<R>;
    sendRequest<P, R, PR, E, RO>(type: ProtocolRequestType<P, R, PR, E, RO>, params: P, token?: CancellationToken): Promise<R>;
    sendRequest<R, E>(type: RequestType0<R, E>, token?: CancellationToken): Promise<R>;
    sendRequest<P, R, E>(type: RequestType<P, R, E>, params: P, token?: CancellationToken): Promise<R>;
    sendRequest<R>(method: string, token?: CancellationToken): Promise<R>;
    sendRequest<R>(method: string, param: any, token?: CancellationToken): Promise<R>;
    onRequest<R, PR, E, RO>(type: ProtocolRequestType0<R, PR, E, RO>, handler: RequestHandler0<R, E>): Disposable;
    onRequest<P, R, PR, E, RO>(type: ProtocolRequestType<P, R, PR, E, RO>, handler: RequestHandler<P, R, E>): Disposable;
    onRequest<R, E>(type: RequestType0<R, E>, handler: RequestHandler0<R, E>): Disposable;
    onRequest<P, R, E>(type: RequestType<P, R, E>, handler: RequestHandler<P, R, E>): Disposable;
    onRequest<R, E>(method: string, handler: GenericRequestHandler<R, E>): Disposable;
    sendNotification<RO>(type: ProtocolNotificationType0<RO>): Promise<void>;
    sendNotification<P, RO>(type: ProtocolNotificationType<P, RO>, params?: P): Promise<void>;
    sendNotification(type: NotificationType0): Promise<void>;
    sendNotification<P>(type: NotificationType<P>, params?: P): Promise<void>;
    sendNotification(method: string): Promise<void>;
    sendNotification(method: string, params: any): Promise<void>;
    onNotification<RO>(type: ProtocolNotificationType0<RO>, handler: NotificationHandler0): Disposable;
    onNotification<P, RO>(type: ProtocolNotificationType<P, RO>, handler: NotificationHandler<P>): Disposable;
    onNotification(type: NotificationType0, handler: NotificationHandler0): Disposable;
    onNotification<P>(type: NotificationType<P>, handler: NotificationHandler<P>): Disposable;
    onNotification(method: string, handler: GenericNotificationHandler): Disposable;
    onProgress<P>(type: ProgressType<P>, token: string | number, handler: NotificationHandler<P>): Disposable;
    info(message: string, data?: any, showNotification?: boolean): void;
    warn(message: string, data?: any, showNotification?: boolean): void;
    error(message: string, data?: any, showNotification?: boolean | 'force'): void;
    handleFailedRequest<T>(type: MessageSignature, token: CancellationToken | undefined, error: any, defaultValue: T, showNotification?: boolean): T;
    hasDedicatedTextSynchronizationFeature(textDocument: TextDocument): boolean;
    getFeature(request: typeof DidOpenTextDocumentNotification.method): DidOpenTextDocumentFeatureShape;
    getFeature(request: typeof DidChangeTextDocumentNotification.method): DidChangeTextDocumentFeatureShape;
    getFeature(request: typeof WillSaveTextDocumentNotification.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentSendFeature<(textDocument: TextDocument) => Promise<void>>;
    getFeature(request: typeof WillSaveTextDocumentWaitUntilRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentSendFeature<(textDocument: TextDocument) => ProviderResult<VTextEdit[]>>;
    getFeature(request: typeof DidSaveTextDocumentNotification.method): DidSaveTextDocumentFeatureShape;
    getFeature(request: typeof DidCloseTextDocumentNotification.method): DidCloseTextDocumentFeatureShape;
    getFeature(request: typeof DidCreateFilesNotification.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileCreateEvent) => Promise<void>;
    };
    getFeature(request: typeof DidRenameFilesNotification.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileRenameEvent) => Promise<void>;
    };
    getFeature(request: typeof DidDeleteFilesNotification.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileDeleteEvent) => Promise<void>;
    };
    getFeature(request: typeof WillCreateFilesRequest.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileWillCreateEvent) => Promise<void>;
    };
    getFeature(request: typeof WillRenameFilesRequest.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileWillRenameEvent) => Promise<void>;
    };
    getFeature(request: typeof WillDeleteFilesRequest.method): DynamicFeature<FileOperationRegistrationOptions> & {
        send: (event: FileWillDeleteEvent) => Promise<void>;
    };
    getFeature(request: typeof CompletionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CompletionItemProvider>;
    getFeature(request: typeof HoverRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<HoverProvider>;
    getFeature(request: typeof SignatureHelpRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<SignatureHelpProvider>;
    getFeature(request: typeof DefinitionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DefinitionProvider>;
    getFeature(request: typeof ReferencesRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<ReferenceProvider>;
    getFeature(request: typeof DocumentHighlightRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentHighlightProvider>;
    getFeature(request: typeof CodeActionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CodeActionProvider>;
    getFeature(request: typeof CodeLensRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CodeLensProviderShape>;
    getFeature(request: typeof DocumentFormattingRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentFormattingEditProvider>;
    getFeature(request: typeof DocumentRangeFormattingRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentRangeFormattingEditProvider>;
    getFeature(request: typeof DocumentOnTypeFormattingRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<OnTypeFormattingEditProvider>;
    getFeature(request: typeof RenameRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<RenameProvider>;
    getFeature(request: typeof DocumentSymbolRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentSymbolProvider>;
    getFeature(request: typeof DocumentLinkRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentLinkProvider>;
    getFeature(request: typeof DocumentColorRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DocumentColorProvider>;
    getFeature(request: typeof DeclarationRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DeclarationProvider>;
    getFeature(request: typeof FoldingRangeRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<FoldingRangeProvider>;
    getFeature(request: typeof ImplementationRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<ImplementationProvider>;
    getFeature(request: typeof SelectionRangeRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<SelectionRangeProvider>;
    getFeature(request: typeof TypeDefinitionRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<TypeDefinitionProvider>;
    getFeature(request: typeof CallHierarchyPrepareRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<CallHierarchyProvider>;
    getFeature(request: typeof SemanticTokensRegistrationType.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<SemanticTokensProviderShape>;
    getFeature(request: typeof LinkedEditingRangeRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<LinkedEditingRangeProvider>;
    getFeature(request: typeof TypeHierarchyPrepareRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<TypeHierarchyProvider>;
    getFeature(request: typeof InlineValueRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<InlineValueProviderShape>;
    getFeature(request: typeof InlayHintRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<InlayHintsProviderShape>;
    getFeature(request: typeof WorkspaceSymbolRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & WorkspaceProviderFeature<WorkspaceSymbolProvider>;
    getFeature(request: typeof DocumentDiagnosticRequest.method): DynamicFeature<TextDocumentRegistrationOptions> & TextDocumentProviderFeature<DiagnosticProviderShape> | undefined;
    getFeature(request: typeof NotebookDocumentSyncRegistrationType.method): DynamicFeature<NotebookDocumentSyncRegistrationOptions> & NotebookDocumentProviderShape | undefined;
}
export {};
