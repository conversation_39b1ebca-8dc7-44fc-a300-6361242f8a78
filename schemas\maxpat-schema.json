{"$schema": "http://json-schema.org/draft-07/schema#", "title": "MaxPat File Schema", "description": "JSON Schema for Max/MSP .maxpat files", "type": "object", "required": ["patcher"], "properties": {"patcher": {"type": "object", "required": ["fileversion"], "properties": {"fileversion": {"type": "integer", "description": "File format version"}, "appversion": {"type": "object", "properties": {"major": {"type": "integer"}, "minor": {"type": "integer"}, "revision": {"type": "integer"}, "architecture": {"type": "string"}, "modernui": {"type": "integer"}}}, "classnamespace": {"type": "string", "description": "Class namespace for objects"}, "rect": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Patcher window rectangle [x, y, width, height]"}, "bglocked": {"type": "integer"}, "openinpresentation": {"type": "integer"}, "default_fontsize": {"type": "number"}, "default_fontface": {"type": "integer"}, "default_fontname": {"type": "string"}, "gridonopen": {"type": "integer"}, "gridsize": {"type": "array", "items": {"type": "number"}, "minItems": 2, "maxItems": 2}, "gridsnaponopen": {"type": "integer"}, "objectsnaponopen": {"type": "integer"}, "statusbarvisible": {"type": "integer"}, "toolbarvisible": {"type": "integer"}, "lefttoolbarpinned": {"type": "integer"}, "toptoolbarpinned": {"type": "integer"}, "righttoolbarpinned": {"type": "integer"}, "bottomtoolbarpinned": {"type": "integer"}, "toolbars_unpinned_last_save": {"type": "integer"}, "tallnewobj": {"type": "integer"}, "boxanimatetime": {"type": "integer"}, "enablehscroll": {"type": "integer"}, "enablevscroll": {"type": "integer"}, "devicewidth": {"type": "number"}, "description": {"type": "string"}, "digest": {"type": "string"}, "tags": {"type": "string"}, "style": {"type": "string"}, "subpatcher_template": {"type": "string"}, "assistshowspatchername": {"type": "integer"}, "boxes": {"type": "array", "items": {"$ref": "#/definitions/boxContainer"}}, "lines": {"type": "array", "items": {"$ref": "#/definitions/lineContainer"}}, "dependency_cache": {"type": "array", "items": {"type": "string"}}, "autosave": {"type": "integer"}}}}, "definitions": {"boxContainer": {"type": "object", "required": ["box"], "properties": {"box": {"$ref": "#/definitions/box"}}}, "box": {"type": "object", "required": ["id", "maxclass"], "properties": {"id": {"type": "string", "pattern": "^obj-\\d+$", "description": "Unique identifier for the box"}, "maxclass": {"type": "string", "description": "Max object class name"}, "text": {"type": "string", "description": "Text content of the object"}, "numinlets": {"type": "integer", "minimum": 0, "description": "Number of inlets"}, "numoutlets": {"type": "integer", "minimum": 0, "description": "Number of outlets"}, "outlettype": {"type": "array", "items": {"type": "string"}, "description": "Types of each outlet"}, "patching_rect": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Position and size [x, y, width, height]"}, "varname": {"type": "string", "description": "Variable name for scripting"}, "patcher": {"$ref": "#/properties/patcher", "description": "Nested patcher for subpatchers"}}}, "lineContainer": {"type": "object", "required": ["patchline"], "properties": {"patchline": {"$ref": "#/definitions/patchline"}}}, "patchline": {"type": "object", "required": ["source", "destination"], "properties": {"source": {"type": "array", "items": [{"type": "string", "pattern": "^obj-\\d+$"}, {"type": "integer", "minimum": 0}], "minItems": 2, "maxItems": 2, "description": "Source [object_id, outlet_index]"}, "destination": {"type": "array", "items": [{"type": "string", "pattern": "^obj-\\d+$"}, {"type": "integer", "minimum": 0}], "minItems": 2, "maxItems": 2, "description": "Destination [object_id, inlet_index]"}, "order": {"type": "integer", "description": "Connection order for multiple connections from same outlet"}, "hidden": {"type": "integer", "description": "Whether the connection is hidden"}}}}}