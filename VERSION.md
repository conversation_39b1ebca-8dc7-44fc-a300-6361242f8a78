# MaxPat Parser VS Code Extension - Version History

## Version 0.2.0 (Current Development)

### 🎯 Major Features Added
- **Problems Tab Integration** - Validation errors now display in VS Code's Problems panel
  - Real-time validation on document changes (debounced to 500ms)
  - Diagnostic collection with proper severity mapping
  - Line-specific error highlighting with ranges
  - Maintains backward compatibility with popup messages for manual validation

### 🔧 Technical Improvements
- Enhanced `ValidationError` interface with `range` property for precise error location
- Added helper functions for line number detection:
  - `findLineNumber()` - Locates specific patterns in document
  - `findPatchlineRange()` - Finds exact line ranges for patchline objects
- Improved validation functions to include document context:
  - `validateBoxes()` now accepts document parameter
  - `validateConnections()` enhanced with range detection
  - `validateSignalFlow()` updated for consistency
- Real-time validation with debouncing to prevent excessive calls
- Diagnostic collection lifecycle management

### 🐛 Bug Fixes
- Fixed extension debugging configuration issues
- Improved error handling for malformed JSON files
- Enhanced patchline range detection for better error positioning

### 📝 Documentation
- Created comprehensive `PROJECT_OUTLINE.md` with project structure and roadmap
- Added known issues tracking
- Documented technical architecture and validation pipeline

### 🔄 Breaking Changes
- None - maintains full backward compatibility

---

## Version 0.1.0 (Initial Release)

### 🎯 Core Features
- **JSON Schema Validation** - Complete structural validation of MaxPat files
- **Connection Validation** - Advanced patchline validation with inlet/outlet bounds checking
- **Command Integration** - "MaxPat: Validate Connections" command
- **File Association** - `.maxpat` and `.maxhelp` file support
- **Language Configuration** - Proper JSON-based language mode

### 🎨 Language Features
- **Syntax Highlighting** - TextMate grammar for MaxPat JSON structure
- **Auto-completion** - Max object name suggestions in `maxclass` fields
- **Hover Information** - Documentation for Max objects (15+ objects supported)

### 🔍 Validation Logic
- **Object Existence** - Validates patchline source/destination objects exist
- **Inlet/Outlet Bounds** - Checks valid inlet/outlet indices
- **Signal Flow Analysis** - Warns about signal/control flow mismatches
- **Duplicate Detection** - Identifies duplicate object IDs
- **Error Reporting** - Detailed error messages with severity levels

### 🛠️ Development Infrastructure
- **TypeScript Setup** - Strongly typed implementation (430+ lines)
- **Test Suite** - Comprehensive unit tests with fixtures
- **Build System** - npm scripts for compile/watch/package
- **Debug Configuration** - F5 debugging support
- **Extension Packaging** - VSIX generation and installation

### 📊 Technical Specifications
- **Extension Size**: ~22KB packaged
- **Max Objects Documented**: 15+ common objects
- **VS Code Compatibility**: 1.74.0+
- **Publisher**: DrewIsAFK

### 🎯 Validation Coverage
- Box structure validation (missing objects, duplicate IDs)
- Patchline connection validation (source/destination existence)
- Inlet/outlet bounds checking with detailed error messages
- Signal flow compatibility warnings
- JSON schema compliance

---

## Development Notes

### Version Numbering Scheme
- **Major.Minor.Patch** (Semantic Versioning)
- **Major**: Breaking changes or significant feature additions
- **Minor**: New features, enhancements, backward compatible
- **Patch**: Bug fixes, small improvements

### Release Process
1. Update version number in `package.json`
2. Update `VERSION.md` with changes
3. Compile TypeScript: `npx tsc`
4. Package extension: `npx vsce package`
5. Test installation and functionality
6. Document any known issues

### Known Issues (Current)
- **Hover Provider**: Inconsistent behavior with special characters (e.g., `ezdac~`)
- **Extension Installation**: Terminal installation affects different VS Code instance
- **Line Detection**: Patchline range detection could be more precise

### Future Roadmap
- Enhanced Max object database
- Visual patch representation
- Max/MSP application integration
- Advanced analysis tools
- Quick fixes and code actions
