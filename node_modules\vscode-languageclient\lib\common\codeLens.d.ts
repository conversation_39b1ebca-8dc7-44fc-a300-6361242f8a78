import { Disposable, TextDocument, ProviderResult, EventEmitter, CodeLensProvider, CodeLens as VCodeLens } from 'vscode';
import { ClientCapabilities, CancellationToken, ServerCapabilities, DocumentSelector, CodeLensOptions, CodeLensRegistrationOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient } from './features';
export interface ProvideCodeLensesSignature {
    (this: void, document: TextDocument, token: CancellationToken): ProviderResult<VCodeLens[]>;
}
export interface ResolveCodeLensSignature {
    (this: void, codeLens: VCodeLens, token: CancellationToken): ProviderResult<VCodeLens>;
}
export interface CodeLensMiddleware {
    provideCodeLenses?: (this: void, document: TextDocument, token: CancellationToken, next: ProvideCodeLensesSignature) => ProviderResult<VCodeLens[]>;
    resolveCodeLens?: (this: void, codeLens: VCode<PERSON><PERSON>, token: CancellationToken, next: ResolveCodeLensSignature) => ProviderResult<VCodeLens>;
}
export declare type CodeLensProviderShape = {
    provider?: CodeLensProvider;
    onDidChangeCodeLensEmitter: EventEmitter<void>;
};
export declare class CodeLensFeature extends TextDocumentLanguageFeature<CodeLensOptions, CodeLensRegistrationOptions, CodeLensProviderShape, CodeLensMiddleware> {
    constructor(client: FeatureClient<CodeLensMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: CodeLensRegistrationOptions): [Disposable, CodeLensProviderShape];
}
