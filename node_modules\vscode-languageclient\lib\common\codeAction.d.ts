import { Disposable, TextDocument, ProviderResult, Range as VRange, Command as VCommand, CodeAction as VCodeAction, CodeActionContext as VCodeActionContext, CodeActionProvider } from 'vscode';
import { ClientCapabilities, CancellationToken, ServerCapabilities, DocumentSelector, CodeActionOptions, CodeActionRegistrationOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient } from './features';
export interface ProvideCodeActionsSignature {
    (this: void, document: TextDocument, range: VRange, context: VCodeActionContext, token: CancellationToken): ProviderResult<(VCommand | VCodeAction)[]>;
}
export interface ResolveCodeActionSignature {
    (this: void, item: VCodeAction, token: CancellationToken): ProviderResult<VCodeAction>;
}
export interface CodeActionMiddleware {
    provideCodeActions?: (this: void, document: TextDocument, range: VRange, context: VCodeActionContext, token: CancellationToken, next: ProvideCodeActionsSignature) => ProviderResult<(VCommand | VCodeAction)[]>;
    resolveCodeAction?: (this: void, item: VCodeAction, token: CancellationToken, next: ResolveCodeActionSignature) => ProviderResult<VCodeAction>;
}
export declare class CodeActionFeature extends TextDocumentLanguageFeature<boolean | CodeActionOptions, CodeActionRegistrationOptions, CodeActionProvider, CodeActionMiddleware> {
    constructor(client: FeatureClient<CodeActionMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: CodeActionRegistrationOptions): [Disposable, CodeActionProvider];
}
