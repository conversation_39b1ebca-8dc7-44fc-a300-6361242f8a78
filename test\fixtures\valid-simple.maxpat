{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [91.0, 106.0, 640.0, 480.0], "boxes": [{"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [279.0, 149.0, 66.0, 22.0], "text": "cycle~ 440", "varname": "osc1"}}, {"box": {"id": "obj-2", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [284.0, 272.0, 45.0, 45.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 1], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-2", 0], "order": 1, "source": ["obj-1", 0]}}], "dependency_cache": [], "autosave": 0}}