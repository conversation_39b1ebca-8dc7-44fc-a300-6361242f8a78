import { TextDocument, Disposable, Position as VPosition, CancellationToken, ProviderResult, SignatureHelpProvider, SignatureHelpContext as VSignatureHelpContext, SignatureHelp as VSignatureHelp } from 'vscode';
import { ClientCapabilities, DocumentSelector, ServerCapabilities, SignatureHelpOptions, SignatureHelpRegistrationOptions } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature, DocumentSelectorOptions } from './features';
export interface ProvideSignatureHelpSignature {
    (this: void, document: TextDocument, position: VPosition, context: VSignatureHelpContext, token: CancellationToken): ProviderResult<VSignatureHelp>;
}
export interface SignatureHelpMiddleware {
    provideSignatureHelp?: (this: void, document: TextDocument, position: VPosition, context: VSignatureHelpContext, token: CancellationToken, next: ProvideSignatureHelpSignature) => ProviderResult<VSignatureHelp>;
}
export declare class SignatureHelpFeature extends TextDocumentLanguageFeature<SignatureHelpOptions, SignatureHelpRegistrationOptions, SignatureHelpProvider, SignatureHelpMiddleware> {
    constructor(client: FeatureClient<SignatureHelpMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: SignatureHelpRegistrationOptions & DocumentSelectorOptions): [Disposable, SignatureHelpProvider];
    private registerProvider;
}
