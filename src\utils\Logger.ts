import * as vscode from 'vscode';

export class Logger {
    private static instance: Logger;
    private outputChannel?: vscode.OutputChannel;
    
    private constructor() {
        const packageJson = require('../../../package.json');
        const version = packageJson.version;
        console.log(`[Logger v${version}] Creating output channel "MaxPat Parser"...`);
        this.outputChannel = vscode.window.createOutputChannel('MaxPat Parser');
        console.log(`[Logger v${version}] Output channel created:`, this.outputChannel ? 'SUCCESS' : 'FAILED');
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    private isDebugEnabled(): boolean {
        return vscode.workspace.getConfiguration('maxpatParser').get('enableDebugLogging', true);
    }

    public error(message: string, ...optionalParams: any[]): void {
        console.log(`[ERROR] ${message}`, ...optionalParams);
        this.outputChannel?.appendLine(`[ERROR] ${message}`);
    }

    public info(message: string, ...optionalParams: any[]): void {
        console.log(`[INFO] ${message}`, ...optionalParams);
        this.outputChannel?.appendLine(`[INFO] ${message}`);
    }

    public debug(message: string, ...optionalParams: any[]): void {
        if (this.isDebugEnabled()) {
            console.debug(`[DEBUG] ${message}`, ...optionalParams);
            this.outputChannel?.appendLine(`[DEBUG] ${message}`);
        }
    }

    public show(): void {
        console.log('[Logger] Attempting to show output channel...');
        console.log('[Logger] Output channel exists:', this.outputChannel ? 'YES' : 'NO');
        this.outputChannel?.show();
        console.log('[Logger] show() called on output channel');
    }

    public dispose(): void {
        this.outputChannel?.dispose();
    }
}
