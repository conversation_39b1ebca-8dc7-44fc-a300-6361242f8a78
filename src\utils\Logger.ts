import * as vscode from 'vscode';

export class Logger {
    private static instance: Logger;
    private outputChannel?: vscode.OutputChannel;
    
    private constructor() {
        this.outputChannel = vscode.window.createOutputChannel('MaxPat Parser');
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    private isDebugEnabled(): boolean {
        return vscode.workspace.getConfiguration('maxpatParser').get('enableDebugLogging', true);
    }

    public error(message: string, ...optionalParams: any[]): void {
        console.log(`[ERROR] ${message}`, ...optionalParams);
        this.outputChannel?.appendLine(`[ERROR] ${message}`);
    }

    public info(message: string, ...optionalParams: any[]): void {
        console.log(`[INFO] ${message}`, ...optionalParams);
        this.outputChannel?.appendLine(`[INFO] ${message}`);
    }

    public debug(message: string, ...optionalParams: any[]): void {
        if (this.isDebugEnabled()) {
            console.debug(`[DEBUG] ${message}`, ...optionalParams);
            this.outputChannel?.appendLine(`[DEBUG] ${message}`);
        }
    }

    public show(): void {
        this.outputChannel?.show();
    }

    public dispose(): void {
        this.outputChannel?.dispose();
    }
}
