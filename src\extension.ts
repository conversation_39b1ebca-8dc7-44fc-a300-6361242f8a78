import * as vscode from 'vscode';
import { Logger } from './utils/Logger';

// Create diagnostic collection for validation errors
const diagnosticCollection = vscode.languages.createDiagnosticCollection('maxpat');

export function activate(context: vscode.ExtensionContext) {
  // Get version from package.json
  const packageJson = require('../../package.json');
  const version = packageJson.version;

  console.log(`[Extension v${version}] Starting activation...`);

  let logger: Logger;

  try {
    console.log(`[Extension v${version}] Importing Logger...`);
    logger = Logger.getInstance();
    console.log(`[Extension v${version}] Logger instance obtained`);

    // Show output channel immediately like Comment Pro does
    console.log(`[Extension v${version}] Calling logger.show()...`);
    logger.show();
    console.log(`[Extension v${version}] logger.show() completed`);

    logger.info(`MaxPat Parser extension v${version} is now active!`);
    logger.info('Extension activated successfully');

    // Force show a message to confirm activation and logger creation
    vscode.window.showInformationMessage(`MaxPat Parser v${version} extension activated - Logger created and shown!`);

    // Additional debug popup to confirm channel name
    setTimeout(() => {
      vscode.window.showInformationMessage('Look for "MaxPat Parser" in Output dropdown (with capital M and P)');
    }, 1000);
  } catch (error) {
    console.error(`[Extension v${version}] Error during activation:`, error);
    vscode.window.showErrorMessage(`MaxPat Parser v${version} activation failed: ${error}`);
    return; // Exit early if logger creation fails
  }

  // Add diagnostic collection to subscriptions
  context.subscriptions.push(diagnosticCollection);

  // Create a test diagnostic to verify the system works
  logger.info('Creating test diagnostic...');
  setTimeout(() => {
    logger.info('Running diagnostic test...');

    const activeEditor = vscode.window.activeTextEditor;
    if (activeEditor) {
      logger.info(`Setting test diagnostic on active file: ${activeEditor.document.fileName}`);
      const testDiagnostic = new vscode.Diagnostic(
        new vscode.Range(0, 0, 0, 10),
        'TEST: Extension diagnostic system is working',
        vscode.DiagnosticSeverity.Error
      );

      // Set the diagnostic
      diagnosticCollection.set(activeEditor.document.uri, [testDiagnostic]);

      // Force show the Problems panel
      vscode.commands.executeCommand('workbench.actions.view.problems');
      logger.info('Test diagnostic set and Problems panel opened');
    } else {
      logger.info('No active editor found for test diagnostic');
    }
  }, 2000); // Wait 2 seconds for everything to initialize

  // Register command for validating connections
  const validateCommand = vscode.commands.registerCommand('maxpat.validateConnections', () => {
    logger.info('Manual validation command triggered');
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
      logger.error('No active editor found');
      vscode.window.showErrorMessage('No active editor found');
      return;
    }

    logger.info(`Active editor file: ${editor.document.fileName}`);
    if (!editor.document.fileName.endsWith('.maxpat')) {
      logger.error('Current file is not a .maxpat file');
      vscode.window.showErrorMessage('Current file is not a .maxpat file');
      return;
    }

    validateMaxPatConnections(editor.document);
  });

  context.subscriptions.push(validateCommand);

  // Register document validation on save and change
  const onSave = vscode.workspace.onDidSaveTextDocument((document) => {
    logger.debug(`Document saved: ${document.fileName}, language: ${document.languageId}`);
    if (document.languageId === 'maxpat') {
      logger.info('Triggering validation on save');
      validateMaxPatConnections(document);
    }
  });

  const onDidChangeTextDocument = vscode.workspace.onDidChangeTextDocument((event) => {
    if (event.document.languageId === 'maxpat') {
      logger.debug(`Document changed: ${event.document.fileName}`);
      // Debounce validation to avoid excessive calls
      clearTimeout((global as any).maxpatValidationTimeout);
      (global as any).maxpatValidationTimeout = setTimeout(() => {
        logger.debug('Triggering debounced validation on change');
        validateMaxPatConnections(event.document);
      }, 500);
    }
  });

  context.subscriptions.push(onSave, onDidChangeTextDocument);

  // Debug: Log when any document is opened
  const onDidOpenTextDocument = vscode.workspace.onDidOpenTextDocument((document) => {
    logger.debug(`Document opened: ${document.fileName}, language: ${document.languageId}`);
    if (document.fileName.endsWith('.maxpat')) {
      logger.info('This is a .maxpat file - should trigger validation');
      if (document.languageId !== 'maxpat') {
        logger.debug(`WARNING: Language ID is '${document.languageId}' but should be 'maxpat'`);
      }
    }
  });

  context.subscriptions.push(onDidOpenTextDocument);

  // Test diagnostic collection immediately
  logger.info('Testing diagnostic collection...');
  const testDiagnostic = new vscode.Diagnostic(
    new vscode.Range(0, 0, 0, 10),
    'Test diagnostic - extension is working',
    vscode.DiagnosticSeverity.Information
  );

  // Apply test diagnostic to any open maxpat files
  vscode.workspace.textDocuments.forEach(doc => {
    if (doc.languageId === 'maxpat') {
      diagnosticCollection.set(doc.uri, [testDiagnostic]);
      logger.info(`Set test diagnostic for: ${doc.fileName}`);
    }
  });

  // Register hover provider for Max objects
  const hoverProvider = vscode.languages.registerHoverProvider('maxpat', {
    provideHover(document, position) {
      return provideMaxPatHover(document, position);
    }
  });

  context.subscriptions.push(hoverProvider);

  // Register completion provider for Max object names
  const completionProvider = vscode.languages.registerCompletionItemProvider('maxpat', {
    provideCompletionItems(document, position) {
      return provideMaxPatCompletions(document, position);
    }
  }, '"'); // Trigger on quote character

  context.subscriptions.push(completionProvider);
}

interface MaxPatBox {
  id: string;
  maxclass: string;
  text?: string;
  numinlets?: number;
  numoutlets?: number;
  outlettype?: string[];
  patching_rect?: number[];
  varname?: string;
}

interface MaxPatPatchline {
  source: [string, number];
  destination: [string, number];
  order?: number;
  hidden?: number;
}

interface ValidationError {
  message: string;
  line?: number;
  range?: vscode.Range;
  severity: 'error' | 'warning' | 'info';
}

// Helper function to find line number of a specific pattern in document
function findLineNumber(document: vscode.TextDocument, searchPattern: string): number {
  const text = document.getText();
  const lines = text.split('\n');

  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(searchPattern)) {
      return i + 1; // Return 1-based line number
    }
  }
  return 1; // Default to line 1 if not found
}

// Helper function to find line range for a patchline by index
function findPatchlineRange(document: vscode.TextDocument, patchlineIndex: number): vscode.Range {
  const text = document.getText();
  const lines = text.split('\n');

  let patchlineCount = 0;
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('"patchline"')) {
      if (patchlineCount === patchlineIndex) {
        // Find the start and end of this patchline object
        let startLine = i;
        let endLine = i;

        // Look backwards for opening brace
        for (let j = i; j >= 0; j--) {
          if (lines[j].trim() === '{') {
            startLine = j;
            break;
          }
        }

        // Look forwards for closing brace
        let braceCount = 0;
        for (let j = startLine; j < lines.length; j++) {
          const line = lines[j];
          braceCount += (line.match(/\{/g) || []).length;
          braceCount -= (line.match(/\}/g) || []).length;
          if (braceCount === 0 && j > startLine) {
            endLine = j;
            break;
          }
        }

        return new vscode.Range(startLine, 0, endLine, lines[endLine].length);
      }
      patchlineCount++;
    }
  }

  return new vscode.Range(0, 0, 0, 100); // Default range
}

function validateMaxPatConnections(document: vscode.TextDocument) {
  const logger = Logger.getInstance();
  logger.info(`Starting validation for: ${document.fileName}`);
  logger.debug(`Document language ID: ${document.languageId}`);

  try {
    const content = document.getText();
    logger.debug(`Document content length: ${content.length} characters`);

    const maxpat = JSON.parse(content);
    logger.debug('JSON parsing successful');

    if (!maxpat.patcher) {
      logger.error('Missing patcher object');
      const diagnostic = new vscode.Diagnostic(
        new vscode.Range(0, 0, 0, content.length),
        'Invalid MaxPat file: missing patcher object',
        vscode.DiagnosticSeverity.Error
      );
      diagnosticCollection.set(document.uri, [diagnostic]);
      logger.debug('Set diagnostic for missing patcher');
      return;
    }

    const patcher = maxpat.patcher;
    const boxes = patcher.boxes || [];
    const lines = patcher.lines || [];

    logger.debug(`Found ${boxes.length} boxes and ${lines.length} lines`);

    // Create a map of box IDs to box objects
    const boxMap = new Map<string, MaxPatBox>();
    boxes.forEach((boxContainer: any) => {
      if (boxContainer.box && boxContainer.box.id) {
        boxMap.set(boxContainer.box.id, boxContainer.box);
      }
    });

    logger.debug(`Created box map with ${boxMap.size} entries`);

    const errors: ValidationError[] = [];

    // Validate box structure
    validateBoxes(document, boxes, errors);
    logger.debug(`After box validation: ${errors.length} errors`);

    // Validate connections
    validateConnections(document, lines, boxMap, errors);
    logger.debug(`After connection validation: ${errors.length} errors`);

    // Validate signal flow
    validateSignalFlow(document, lines, boxMap, errors);
    logger.debug(`After signal flow validation: ${errors.length} errors`);

    // Convert errors to diagnostics and display
    displayValidationResults(document, errors);

  } catch (error) {
    const diagnostic = new vscode.Diagnostic(
      new vscode.Range(0, 0, 0, 0),
      `Error parsing MaxPat file: ${error}`,
      vscode.DiagnosticSeverity.Error
    );
    diagnosticCollection.set(document.uri, [diagnostic]);
  }
}

function validateBoxes(document: vscode.TextDocument, boxes: any[], errors: ValidationError[]) {
  const usedIds = new Set<string>();

  boxes.forEach((boxContainer: any, index: number) => {
    if (!boxContainer.box) {
      errors.push({
        message: `Box ${index + 1}: Missing box object`,
        severity: 'error'
      });
      return;
    }

    const box = boxContainer.box;

    // Check for required fields
    if (!box.id) {
      errors.push({
        message: `Box ${index + 1}: Missing id`,
        severity: 'error'
      });
    } else {
      // Check for duplicate IDs
      if (usedIds.has(box.id)) {
        errors.push({
          message: `Box ${index + 1}: Duplicate ID '${box.id}'`,
          severity: 'error'
        });
      }
      usedIds.add(box.id);
    }

    if (!box.maxclass) {
      errors.push({
        message: `Box ${index + 1} (${box.id}): Missing maxclass`,
        severity: 'error'
      });
    }

    // Validate inlet/outlet counts
    if (box.numinlets !== undefined && box.numinlets < 0) {
      errors.push({
        message: `Box ${box.id}: Invalid numinlets (${box.numinlets})`,
        severity: 'error'
      });
    }

    if (box.numoutlets !== undefined && box.numoutlets < 0) {
      errors.push({
        message: `Box ${box.id}: Invalid numoutlets (${box.numoutlets})`,
        severity: 'error'
      });
    }

    // Validate outlet types match outlet count
    if (box.outlettype && box.numoutlets !== undefined) {
      if (box.outlettype.length !== box.numoutlets) {
        errors.push({
          message: `Box ${box.id}: Outlet type count (${box.outlettype.length}) doesn't match numoutlets (${box.numoutlets})`,
          severity: 'warning'
        });
      }
    }
  });
}

function validateConnections(document: vscode.TextDocument, lines: any[], boxMap: Map<string, MaxPatBox>, errors: ValidationError[]) {
  lines.forEach((lineContainer: any, index: number) => {
    const range = findPatchlineRange(document, index);

    if (!lineContainer.patchline) {
      errors.push({
        message: `Line ${index + 1}: Missing patchline object`,
        severity: 'error',
        range: range
      });
      return;
    }

    const line: MaxPatPatchline = lineContainer.patchline;

    if (!line.source || !line.destination) {
      errors.push({
        message: `Line ${index + 1}: Missing source or destination`,
        severity: 'error'
      });
      return;
    }

    const [sourceId, sourceOutlet] = line.source;
    const [destId, destInlet] = line.destination;

    // Check if source box exists
    const sourceBox = boxMap.get(sourceId);
    if (!sourceBox) {
      errors.push({
        message: `Line ${index + 1}: Source box '${sourceId}' not found`,
        severity: 'error',
        range: range
      });
      return;
    }

    // Check if destination box exists
    const destBox = boxMap.get(destId);
    if (!destBox) {
      errors.push({
        message: `Line ${index + 1}: Destination box '${destId}' not found`,
        severity: 'error',
        range: range
      });
      return;
    }

    // Check outlet/inlet bounds
    if (sourceBox.numoutlets !== undefined && sourceOutlet >= sourceBox.numoutlets) {
      errors.push({
        message: `Line ${index + 1}: Source outlet ${sourceOutlet} exceeds available outlets (${sourceBox.numoutlets}) for box '${sourceId}' (${sourceBox.maxclass})`,
        severity: 'error',
        range: range
      });
    }

    if (destBox.numinlets !== undefined && destInlet >= destBox.numinlets) {
      errors.push({
        message: `Line ${index + 1}: Destination inlet ${destInlet} exceeds available inlets (${destBox.numinlets}) for box '${destId}' (${destBox.maxclass})`,
        severity: 'error',
        range: range
      });
    }

    // Check for negative indices
    if (sourceOutlet < 0) {
      errors.push({
        message: `Line ${index + 1}: Invalid source outlet index ${sourceOutlet}`,
        severity: 'error'
      });
    }

    if (destInlet < 0) {
      errors.push({
        message: `Line ${index + 1}: Invalid destination inlet index ${destInlet}`,
        severity: 'error'
      });
    }
  });
}

function validateSignalFlow(document: vscode.TextDocument, lines: any[], boxMap: Map<string, MaxPatBox>, errors: ValidationError[]) {
  // Check for signal/control flow mismatches
  lines.forEach((lineContainer: any, index: number) => {
    if (!lineContainer.patchline) return;

    const line: MaxPatPatchline = lineContainer.patchline;
    const [sourceId, sourceOutlet] = line.source;
    const [destId] = line.destination;

    const sourceBox = boxMap.get(sourceId);
    const destBox = boxMap.get(destId);

    if (!sourceBox || !destBox) return;

    // Check signal flow compatibility
    const sourceIsSignal = sourceBox.maxclass?.includes('~') ||
                          (sourceBox.outlettype && sourceBox.outlettype[sourceOutlet] === 'signal');
    const destIsSignal = destBox.maxclass?.includes('~');

    if (sourceIsSignal && destBox.maxclass && !destIsSignal) {
      errors.push({
        message: `Line ${index + 1}: Signal output from '${sourceBox.maxclass}' connected to non-signal input of '${destBox.maxclass}'`,
        severity: 'warning'
      });
    }

    // Check for common connection mistakes
    if (sourceBox.maxclass === 'bang' && destBox.maxclass?.includes('~')) {
      errors.push({
        message: `Line ${index + 1}: Bang connected to signal object '${destBox.maxclass}' - this may not work as expected`,
        severity: 'info'
      });
    }
  });
}

function displayValidationResults(document: vscode.TextDocument, errors: ValidationError[]) {
  const logger = Logger.getInstance();
  logger.info(`Displaying validation results: ${errors.length} errors found`);

  // Convert validation errors to VS Code diagnostics
  const diagnostics: vscode.Diagnostic[] = errors.map((error, index) => {
    const severity = error.severity === 'error' ? vscode.DiagnosticSeverity.Error :
                    error.severity === 'warning' ? vscode.DiagnosticSeverity.Warning :
                    vscode.DiagnosticSeverity.Information;

    // Use provided range or create a default range
    const range = error.range || new vscode.Range(
      error.line ? error.line - 1 : 0, 0,
      error.line ? error.line - 1 : 0, 100
    );

    logger.debug(`  Error ${index + 1}: ${error.message} (severity: ${error.severity})`);
    logger.debug(`    Range: line ${range.start.line + 1}, char ${range.start.character} to line ${range.end.line + 1}, char ${range.end.character}`);

    return new vscode.Diagnostic(range, error.message, severity);
  });

  logger.debug(`Created ${diagnostics.length} diagnostics`);

  // Set diagnostics for this document
  try {
    // Clear existing diagnostics for this document only (not all documents)
    diagnosticCollection.delete(document.uri);

    // Set new diagnostics
    diagnosticCollection.set(document.uri, diagnostics);

    // Log diagnostic details
    logger.debug(`Set ${diagnostics.length} diagnostics for document: ${document.uri.toString()}`);
    logger.debug(`Diagnostic collection name: ${diagnosticCollection.name}`);

    // Force update of Problems view
    vscode.commands.executeCommand('workbench.actions.view.problems');
    logger.debug('Forced Problems view to update');
  } catch (error) {
    logger.error(`ERROR setting diagnostics: ${error}`);
  }

  // Also show a summary message for manual validation
  if (errors.length === 0) {
    vscode.window.showInformationMessage('MaxPat file is valid! All connections check out.');
  } else {
    const errorCount = errors.filter(e => e.severity === 'error').length;
    const warningCount = errors.filter(e => e.severity === 'warning').length;
    const infoCount = errors.filter(e => e.severity === 'info').length;

    let message = 'MaxPat validation completed:\n';
    if (errorCount > 0) message += `${errorCount} error(s)\n`;
    if (warningCount > 0) message += `${warningCount} warning(s)\n`;
    if (infoCount > 0) message += `${infoCount} info message(s)\n`;

    message += '\nDetails:\n' + errors.map(e => `${e.severity.toUpperCase()}: ${e.message}`).join('\n');

    if (errorCount > 0) {
      vscode.window.showErrorMessage(message);
    } else if (warningCount > 0) {
      vscode.window.showWarningMessage(message);
    } else {
      vscode.window.showInformationMessage(message);
    }
  }
}

// Max object documentation database
const maxObjectDocs: { [key: string]: { description: string; inlets: string[]; outlets: string[] } } = {
  'cycle~': {
    description: 'Cosine wave oscillator. Outputs a cosine wave at the specified frequency.',
    inlets: ['frequency (float/signal)', 'phase offset (float)'],
    outlets: ['signal output']
  },
  'ezdac~': {
    description: 'Easy digital-to-analog converter. Simple audio output object.',
    inlets: ['left channel (signal)', 'right channel (signal)'],
    outlets: []
  },
  'gain~': {
    description: 'Signal gain/volume control with UI slider.',
    inlets: ['signal input'],
    outlets: ['signal output', 'gain value']
  },
  'newobj': {
    description: 'Generic object box. The actual functionality depends on the text content.',
    inlets: ['varies by object'],
    outlets: ['varies by object']
  },
  'message': {
    description: 'Message box. Stores and outputs messages when clicked or triggered.',
    inlets: ['trigger input', 'set input'],
    outlets: ['message output']
  },
  'bang': {
    description: 'Bang button. Outputs a bang message when clicked or triggered.',
    inlets: ['trigger input'],
    outlets: ['bang output']
  },
  'toggle': {
    description: 'Toggle switch. Outputs 0 or 1.',
    inlets: ['trigger input', 'set input'],
    outlets: ['0 or 1 output']
  },
  'number': {
    description: 'Number box for integer values.',
    inlets: ['number input', 'set input'],
    outlets: ['number output']
  },
  'flonum': {
    description: 'Floating point number box.',
    inlets: ['float input', 'set input'],
    outlets: ['float output']
  }
};

function provideMaxPatHover(document: vscode.TextDocument, position: vscode.Position): vscode.Hover | undefined {
  const line = document.lineAt(position.line);
  const lineText = line.text;

  // Check if we're on a line with maxclass
  if (!lineText.includes('"maxclass"')) return undefined;

  // Find the maxclass value on this line using regex
  const maxclassMatch = lineText.match(/"maxclass":\s*"([^"]+)"/);
  if (!maxclassMatch) return undefined;

  const objectName = maxclassMatch[1];

  // Check if the cursor is within the maxclass value
  const valueStart = lineText.indexOf(`"${objectName}"`, lineText.indexOf('"maxclass"'));
  const valueEnd = valueStart + objectName.length + 2; // +2 for quotes

  if (position.character >= valueStart && position.character <= valueEnd) {
    const objectDoc = maxObjectDocs[objectName];
    if (objectDoc) {
      const markdown = new vscode.MarkdownString();
      markdown.appendMarkdown(`**${objectName}**\n\n`);
      markdown.appendMarkdown(`${objectDoc.description}\n\n`);

      if (objectDoc.inlets.length > 0) {
        markdown.appendMarkdown(`**Inlets:**\n`);
        objectDoc.inlets.forEach((inlet, index) => {
          markdown.appendMarkdown(`- ${index}: ${inlet}\n`);
        });
        markdown.appendMarkdown('\n');
      }

      if (objectDoc.outlets.length > 0) {
        markdown.appendMarkdown(`**Outlets:**\n`);
        objectDoc.outlets.forEach((outlet, index) => {
          markdown.appendMarkdown(`- ${index}: ${outlet}\n`);
        });
      }

      // Create a range for the object name
      const hoverRange = new vscode.Range(
        position.line, valueStart,
        position.line, valueEnd
      );

      return new vscode.Hover(markdown, hoverRange);
    }
  }

  return undefined;
}

function provideMaxPatCompletions(document: vscode.TextDocument, position: vscode.Position): vscode.CompletionItem[] {
  const line = document.lineAt(position.line);
  const lineText = line.text;

  // Check if we're in a maxclass field
  if (lineText.includes('"maxclass"') && lineText.includes('"')) {
    const completions: vscode.CompletionItem[] = [];

    Object.keys(maxObjectDocs).forEach(objectName => {
      const completion = new vscode.CompletionItem(objectName, vscode.CompletionItemKind.Class);
      completion.detail = maxObjectDocs[objectName].description;
      completion.documentation = new vscode.MarkdownString(maxObjectDocs[objectName].description);
      completions.push(completion);
    });

    return completions;
  }

  return [];
}

export function deactivate() {
  const logger = Logger.getInstance();
  logger.info('Deactivating MaxPat Parser extension...');
  logger.dispose();
}