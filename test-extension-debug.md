# Testing MaxPat Parser Extension

## ✅ Extension Status
- **Installed**: `drewisafk.maxpat-parser` 
- **Version**: 0.1.0
- **Package Size**: 18.72 KB (much cleaner!)

## 🧪 Testing Steps

### 1. **Check Extension is Active**
- Open `test-debug.maxpat` in VS Code
- Check bottom-right corner - should show "MaxPat" as the language mode
- If it shows "JSON", right-click and select "Configure File Association for '.maxpat'"

### 2. **Test Validation Command**
- Open Command Palette (`Ctrl+Shift+P`)
- Type "MaxPat: Validate Connections"
- Should show: ✅ "MaxPat file is valid! All connections check out."

### 3. **Test Hover Information**
- In `test-debug.maxpat`, hover over `"cycle~"` (line 7)
- Should show documentation popup with:
  - Description: "Cosine wave oscillator..."
  - Inlets: frequency, phase offset
  - Outlets: signal output

### 4. **Test Auto-completion**
- Edit line 7: change `"cycle~"` to `"`
- Should see completion suggestions for Max objects

### 5. **Test Error Detection**
- Change line 19: `["obj-1", 0]` to `["obj-1", 5]` (invalid outlet)
- Save file (`Ctrl+S`)
- Should show error message about invalid outlet index

## 🐛 Debugging Extension Development

### Method 1: Use F5 Debug Launch
1. Open this project folder in VS Code
2. Press `F5` or go to Run & Debug panel
3. Select "Run Extension" configuration
4. This opens a new Extension Development Host window
5. In the new window, open a `.maxpat` file to test

### Method 2: Check Extension Logs
1. Open Command Palette (`Ctrl+Shift+P`)
2. Type "Developer: Reload Window" to reload VS Code
3. Check Output panel → "Log (Extension Host)" for any errors

### Method 3: Manual Testing
1. Open any `.maxpat` file
2. Check if language mode is "MaxPat" (bottom-right)
3. Try the validation command
4. Test hover and completion features

## 🔧 Troubleshooting

### If Extension Doesn't Appear in Extensions List:
```bash
# Check if installed
code --list-extensions | grep maxpat

# Reinstall if needed
code --uninstall-extension drewisafk.maxpat-parser
code --install-extension maxpat-parser-0.1.0.vsix
```

### If Language Mode is Wrong:
- Right-click in `.maxpat` file
- Select "Configure File Association for '.maxpat'"
- Choose "MaxPat" from the list

### If Commands Don't Work:
- Check Developer Console (`Help` → `Toggle Developer Tools`)
- Look for JavaScript errors in Console tab
- Check if extension activated properly

## 📝 Expected Results

With `test-debug.maxpat` open:
- ✅ Language mode: "MaxPat"
- ✅ Syntax highlighting for JSON structure
- ✅ Validation command works
- ✅ Hover shows Max object documentation
- ✅ Auto-completion in maxclass fields
- ✅ Error detection for invalid connections
