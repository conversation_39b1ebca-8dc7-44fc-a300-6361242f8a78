import { TextDocument, Disposable, CancellationToken, ProviderResult, DocumentSymbolProvider, SymbolInformation as VSymbolInformation, DocumentSymbol as VDocumentSymbol } from 'vscode';
import { ClientCapabilities, DocumentSelector, DocumentSymbolOptions, DocumentSymbolRegistrationOptions, ServerCapabilities, SymbolKind, SymbolTag } from 'vscode-languageserver-protocol';
import { FeatureClient, TextDocumentLanguageFeature } from './features';
export declare const SupportedSymbolKinds: SymbolKind[];
export declare const SupportedSymbolTags: SymbolTag[];
export interface ProvideDocumentSymbolsSignature {
    (this: void, document: TextDocument, token: CancellationToken): ProviderResult<VSymbolInformation[] | VDocumentSymbol[]>;
}
export interface DocumentSymbolMiddleware {
    provideDocumentSymbols?: (this: void, document: TextDocument, token: CancellationToken, next: ProvideDocumentSymbolsSignature) => ProviderResult<VSymbolInformation[] | VDocumentSymbol[]>;
}
export declare class DocumentSymbolFeature extends TextDocumentLanguageFeature<boolean | DocumentSymbolOptions, DocumentSymbolRegistrationOptions, DocumentSymbolProvider, DocumentSymbolMiddleware> {
    constructor(client: FeatureClient<DocumentSymbolMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: DocumentSymbolRegistrationOptions): [Disposable, DocumentSymbolProvider];
}
