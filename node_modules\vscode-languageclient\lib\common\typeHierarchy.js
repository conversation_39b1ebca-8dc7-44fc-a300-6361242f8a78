"use strict";
/* --------------------------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 * ------------------------------------------------------------------------------------------ */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeHierarchyFeature = void 0;
const vscode_1 = require("vscode");
const vscode_languageserver_protocol_1 = require("vscode-languageserver-protocol");
const features_1 = require("./features");
class TypeHierarchyProvider {
    constructor(client) {
        this.client = client;
        this.middleware = client.middleware;
    }
    prepareTypeHierarchy(document, position, token) {
        const client = this.client;
        const middleware = this.middleware;
        const prepareTypeHierarchy = (document, position, token) => {
            const params = client.code2ProtocolConverter.asTextDocumentPositionParams(document, position);
            return client.sendRequest(vscode_languageserver_protocol_1.TypeHierarchyPrepareRequest.type, params, token).then((result) => {
                if (token.isCancellationRequested) {
                    return null;
                }
                return client.protocol2CodeConverter.asTypeHierarchyItems(result, token);
            }, (error) => {
                return client.handleFailedRequest(vscode_languageserver_protocol_1.TypeHierarchyPrepareRequest.type, token, error, null);
            });
        };
        return middleware.prepareTypeHierarchy
            ? middleware.prepareTypeHierarchy(document, position, token, prepareTypeHierarchy)
            : prepareTypeHierarchy(document, position, token);
    }
    provideTypeHierarchySupertypes(item, token) {
        const client = this.client;
        const middleware = this.middleware;
        const provideTypeHierarchySupertypes = (item, token) => {
            const params = {
                item: client.code2ProtocolConverter.asTypeHierarchyItem(item)
            };
            return client.sendRequest(vscode_languageserver_protocol_1.TypeHierarchySupertypesRequest.type, params, token).then((result) => {
                if (token.isCancellationRequested) {
                    return null;
                }
                return client.protocol2CodeConverter.asTypeHierarchyItems(result, token);
            }, (error) => {
                return client.handleFailedRequest(vscode_languageserver_protocol_1.TypeHierarchySupertypesRequest.type, token, error, null);
            });
        };
        return middleware.provideTypeHierarchySupertypes
            ? middleware.provideTypeHierarchySupertypes(item, token, provideTypeHierarchySupertypes)
            : provideTypeHierarchySupertypes(item, token);
    }
    provideTypeHierarchySubtypes(item, token) {
        const client = this.client;
        const middleware = this.middleware;
        const provideTypeHierarchySubtypes = (item, token) => {
            const params = {
                item: client.code2ProtocolConverter.asTypeHierarchyItem(item)
            };
            return client.sendRequest(vscode_languageserver_protocol_1.TypeHierarchySubtypesRequest.type, params, token).then((result) => {
                if (token.isCancellationRequested) {
                    return null;
                }
                return client.protocol2CodeConverter.asTypeHierarchyItems(result, token);
            }, (error) => {
                return client.handleFailedRequest(vscode_languageserver_protocol_1.TypeHierarchySubtypesRequest.type, token, error, null);
            });
        };
        return middleware.provideTypeHierarchySubtypes
            ? middleware.provideTypeHierarchySubtypes(item, token, provideTypeHierarchySubtypes)
            : provideTypeHierarchySubtypes(item, token);
    }
}
class TypeHierarchyFeature extends features_1.TextDocumentLanguageFeature {
    constructor(client) {
        super(client, vscode_languageserver_protocol_1.TypeHierarchyPrepareRequest.type);
    }
    fillClientCapabilities(capabilities) {
        const capability = (0, features_1.ensure)((0, features_1.ensure)(capabilities, 'textDocument'), 'typeHierarchy');
        capability.dynamicRegistration = true;
    }
    initialize(capabilities, documentSelector) {
        const [id, options] = this.getRegistration(documentSelector, capabilities.typeHierarchyProvider);
        if (!id || !options) {
            return;
        }
        this.register({ id: id, registerOptions: options });
    }
    registerLanguageProvider(options) {
        const client = this._client;
        const provider = new TypeHierarchyProvider(client);
        return [vscode_1.languages.registerTypeHierarchyProvider(client.protocol2CodeConverter.asDocumentSelector(options.documentSelector), provider), provider];
    }
}
exports.TypeHierarchyFeature = TypeHierarchyFeature;
