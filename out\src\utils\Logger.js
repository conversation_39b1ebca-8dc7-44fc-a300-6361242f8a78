"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const vscode = require("vscode");
class Logger {
    constructor() {
        console.log('[Logger] Creating output channel "MaxPat Parser"...');
        this.outputChannel = vscode.window.createOutputChannel('MaxPat Parser');
        console.log('[Logger] Output channel created:', this.outputChannel ? 'SUCCESS' : 'FAILED');
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    isDebugEnabled() {
        return vscode.workspace.getConfiguration('maxpatParser').get('enableDebugLogging', true);
    }
    error(message, ...optionalParams) {
        console.log(`[ERROR] ${message}`, ...optionalParams);
        this.outputChannel?.appendLine(`[ERROR] ${message}`);
    }
    info(message, ...optionalParams) {
        console.log(`[INFO] ${message}`, ...optionalParams);
        this.outputChannel?.appendLine(`[INFO] ${message}`);
    }
    debug(message, ...optionalParams) {
        if (this.isDebugEnabled()) {
            console.debug(`[DEBUG] ${message}`, ...optionalParams);
            this.outputChannel?.appendLine(`[DEBUG] ${message}`);
        }
    }
    show() {
        console.log('[Logger] Attempting to show output channel...');
        console.log('[Logger] Output channel exists:', this.outputChannel ? 'YES' : 'NO');
        this.outputChannel?.show();
        console.log('[Logger] show() called on output channel');
    }
    dispose() {
        this.outputChannel?.dispose();
    }
}
exports.Logger = Logger;
//# sourceMappingURL=Logger.js.map