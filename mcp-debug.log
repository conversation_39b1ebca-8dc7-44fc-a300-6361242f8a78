{"timestamp":"2025-07-04T08:29:58.361Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"4e35wcjdk9a"}
{"timestamp":"2025-07-04T08:29:58.361Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"8parsiu6ugd"}
{"timestamp":"2025-07-04T08:29:58.361Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"jhy26pcr3fc"}
{"timestamp":"2025-07-04T08:29:58.362Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"kc4t7mccm9"}
{"timestamp":"2025-07-04T08:29:58.362Z","level":"DEBUG","message":"server.connect() called.","correlationId":"uwuv8vvu3i"}
{"timestamp":"2025-07-04T08:29:58.362Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"kh7kde3gcs"}
{"timestamp":"2025-07-04T08:29:58.439Z","level":"DEBUG","message":"[rb7d2e] Completed ListTools request","correlationId":"i4fjlq7s7fj"}
{"timestamp":"2025-07-04T08:29:58.439Z","level":"DEBUG","message":"[rb7d2e] Handling ListTools request","correlationId":"y3411k4agge"}
