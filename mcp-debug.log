{"timestamp":"2025-07-04T16:43:54.744Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"awbc5e701o7"}
{"timestamp":"2025-07-04T16:43:54.744Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"wyd8bskbyzn"}
{"timestamp":"2025-07-04T16:43:54.744Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"hhbi9khz2uv"}
{"timestamp":"2025-07-04T16:43:54.744Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"1lzeog6t0l6i"}
{"timestamp":"2025-07-04T16:43:54.744Z","level":"DEBUG","message":"server.connect() called.","correlationId":"l1ws1b2xjgk"}
{"timestamp":"2025-07-04T16:43:54.745Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"bpl2043aej7"}
{"timestamp":"2025-07-04T16:43:54.761Z","level":"DEBUG","message":"[iqlk5e] Handling ListTools request","correlationId":"nbndyqey5wn"}
{"timestamp":"2025-07-04T16:43:54.761Z","level":"DEBUG","message":"[iqlk5e] Completed ListTools request","correlationId":"ks822pu34h"}
