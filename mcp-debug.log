{"timestamp":"2025-07-04T08:23:13.369Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"lp3ra7x2og"}
{"timestamp":"2025-07-04T08:23:13.370Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"cakhbfy3mdw"}
{"timestamp":"2025-07-04T08:23:13.370Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"2ax06aerxhg"}
{"timestamp":"2025-07-04T08:23:13.370Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"6be3gvt8are"}
{"timestamp":"2025-07-04T08:23:13.371Z","level":"DEBUG","message":"server.connect() called.","correlationId":"uluw72khl6o"}
{"timestamp":"2025-07-04T08:23:13.371Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"yuhqbustxbk"}
{"timestamp":"2025-07-04T08:23:13.377Z","level":"DEBUG","message":"[w8qw48] Completed ListTools request","correlationId":"rm8oote4pr"}
{"timestamp":"2025-07-04T08:23:13.377Z","level":"DEBUG","message":"[w8qw48] Handling ListTools request","correlationId":"tpmz36ohby"}
