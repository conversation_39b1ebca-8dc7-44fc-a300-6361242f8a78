{"timestamp":"2025-07-04T16:32:21.450Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"8pg2r63vw0c"}
{"timestamp":"2025-07-04T16:32:21.451Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"ucmufy1d41j"}
{"timestamp":"2025-07-04T16:32:21.451Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"c0o3sm5nvte"}
{"timestamp":"2025-07-04T16:32:21.451Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"mfdv0180p7l"}
{"timestamp":"2025-07-04T16:32:21.451Z","level":"DEBUG","message":"server.connect() called.","correlationId":"viqxiy9t7wm"}
{"timestamp":"2025-07-04T16:32:21.451Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"al3xpvwn6ap"}
{"timestamp":"2025-07-04T16:32:21.456Z","level":"DEBUG","message":"[2hq34h] Completed ListTools request","correlationId":"t1n3m2zy5wj"}
{"timestamp":"2025-07-04T16:32:21.456Z","level":"DEBUG","message":"[2hq34h] Handling ListTools request","correlationId":"7db6oo2txb"}
