{"timestamp":"2025-07-04T16:37:12.614Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"r75qpq5z8f"}
{"timestamp":"2025-07-04T16:37:12.614Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"yq5k4h2ua0p"}
{"timestamp":"2025-07-04T16:37:12.614Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"qj4oulcf9sj"}
{"timestamp":"2025-07-04T16:37:12.614Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"eld7dngmu86"}
{"timestamp":"2025-07-04T16:37:12.615Z","level":"DEBUG","message":"server.connect() called.","correlationId":"p2llu6njjga"}
{"timestamp":"2025-07-04T16:37:12.615Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"mcgmrhzrs7b"}
{"timestamp":"2025-07-04T16:37:12.630Z","level":"DEBUG","message":"[5qzn1f] Completed ListTools request","correlationId":"x3wmbi1xg78"}
{"timestamp":"2025-07-04T16:37:12.630Z","level":"DEBUG","message":"[5qzn1f] Handling ListTools request","correlationId":"55vl499igfc"}
