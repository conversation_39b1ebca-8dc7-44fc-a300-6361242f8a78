{"timestamp":"2025-07-04T08:37:54.591Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"ity4vineuxg"}
{"timestamp":"2025-07-04T08:37:54.591Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"ycbovlbq80p"}
{"timestamp":"2025-07-04T08:37:54.591Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"4gi4wdckduk"}
{"timestamp":"2025-07-04T08:37:54.591Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"we29h7ifn5l"}
{"timestamp":"2025-07-04T08:37:54.592Z","level":"DEBUG","message":"server.connect() called.","correlationId":"fc5t85fndk5"}
{"timestamp":"2025-07-04T08:37:54.592Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"ffvz4yh2b1w"}
{"timestamp":"2025-07-04T08:37:54.599Z","level":"DEBUG","message":"[0eit8q] Handling ListTools request","correlationId":"v73sr07ba0l"}
{"timestamp":"2025-07-04T08:37:54.599Z","level":"DEBUG","message":"[0eit8q] Completed ListTools request","correlationId":"q14tcdjvx9b"}
