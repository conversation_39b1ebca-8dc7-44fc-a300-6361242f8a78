{"timestamp":"2025-07-04T08:03:53.456Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"nojhw058up9"}
{"timestamp":"2025-07-04T08:03:53.456Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"xmb62uy8d2"}
{"timestamp":"2025-07-04T08:03:53.456Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"2i8cynntjda"}
{"timestamp":"2025-07-04T08:03:53.456Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"m16lhu0h90k"}
{"timestamp":"2025-07-04T08:03:53.457Z","level":"DEBUG","message":"server.connect() called.","correlationId":"j81qzp5bhy"}
{"timestamp":"2025-07-04T08:03:53.457Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"bihzjglihdt"}
{"timestamp":"2025-07-04T08:03:53.546Z","level":"DEBUG","message":"[kq62o] Completed ListTools request","correlationId":"8tnd67iei22"}
{"timestamp":"2025-07-04T08:03:53.546Z","level":"DEBUG","message":"[kq62o] Handling ListTools request","correlationId":"ajzcgm9tzj4"}
