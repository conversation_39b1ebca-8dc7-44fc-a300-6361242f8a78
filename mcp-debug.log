{"timestamp":"2025-07-04T07:11:30.296Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"dkvwsdn74ud"}
{"timestamp":"2025-07-04T07:11:30.296Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"y1t01tym9nh"}
{"timestamp":"2025-07-04T07:11:30.296Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"7kkhybdeqpd"}
{"timestamp":"2025-07-04T07:11:30.296Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"13bbm80qt7ub"}
{"timestamp":"2025-07-04T07:11:30.296Z","level":"DEBUG","message":"server.connect() called.","correlationId":"ixps83szin"}
{"timestamp":"2025-07-04T07:11:30.296Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"pwikbv0aa8s"}
{"timestamp":"2025-07-04T07:11:30.302Z","level":"DEBUG","message":"[wbcodq] Completed ListTools request","correlationId":"ame2dnmwbbs"}
{"timestamp":"2025-07-04T07:11:30.302Z","level":"DEBUG","message":"[wbcodq] Handling ListTools request","correlationId":"zzvue6seo88"}
