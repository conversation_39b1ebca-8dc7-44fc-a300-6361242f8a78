{"timestamp":"2025-07-04T16:11:57.566Z","level":"DEBUG","message":"Entering startServer function.","correlationId":"67meg1slkpa"}
{"timestamp":"2025-07-04T16:11:57.566Z","level":"MCP","message":"Starting MCP server version 0.1.1...","correlationId":"b65123pt53f"}
{"timestamp":"2025-07-04T16:11:57.567Z","level":"DEBUG","message":"Stdio transport created.","correlationId":"jjj6r8r2ws"}
{"timestamp":"2025-07-04T16:11:57.567Z","level":"MCP","message":"Server version 0.1.1 connecting...","correlationId":"cycpeg1vz7g"}
{"timestamp":"2025-07-04T16:11:57.568Z","level":"DEBUG","message":"server.connect() called.","correlationId":"nezki9jif7j"}
{"timestamp":"2025-07-04T16:11:57.568Z","level":"DEBUG","message":"Exiting startServer function scope.","correlationId":"wjkmjm48x6e"}
{"timestamp":"2025-07-04T16:11:57.584Z","level":"DEBUG","message":"[fl2qoi] Handling ListTools request","correlationId":"wablkmt7uj"}
{"timestamp":"2025-07-04T16:11:57.584Z","level":"DEBUG","message":"[fl2qoi] Completed ListTools request","correlationId":"h5y3bmom0vd"}
