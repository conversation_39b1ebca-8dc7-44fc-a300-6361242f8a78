// Quick verification that extension files are correct
const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying MaxPat Parser Extension...\n');

// Check package.json
try {
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log('✅ package.json:');
  console.log(`   - Name: ${pkg.name}`);
  console.log(`   - Publisher: ${pkg.publisher}`);
  console.log(`   - Version: ${pkg.version}`);
  console.log(`   - Main: ${pkg.main}`);
  
  // Check if main file exists
  if (fs.existsSync(pkg.main)) {
    console.log(`   - Main file exists: ${pkg.main}`);
  } else {
    console.log(`   - ❌ Main file missing: ${pkg.main}`);
  }
} catch (error) {
  console.log('❌ package.json error:', error.message);
}

// Check compiled output
console.log('\n✅ Compiled output:');
if (fs.existsSync('out/extension.js')) {
  const stats = fs.statSync('out/extension.js');
  console.log(`   - out/extension.js: ${Math.round(stats.size / 1024)}KB`);
} else {
  console.log('   - ❌ out/extension.js missing');
}

// Check schema
if (fs.existsSync('schemas/maxpat-schema.json')) {
  console.log('   - ✅ maxpat-schema.json exists');
} else {
  console.log('   - ❌ maxpat-schema.json missing');
}

// Check syntax highlighting
if (fs.existsSync('syntaxes/maxpat.tmGrammar.json')) {
  console.log('   - ✅ maxpat.tmGrammar.json exists');
} else {
  console.log('   - ❌ maxpat.tmGrammar.json missing');
}

// Check VSIX package
if (fs.existsSync('maxpat-parser-0.1.0.vsix')) {
  const stats = fs.statSync('maxpat-parser-0.1.0.vsix');
  console.log(`   - ✅ Extension package: ${Math.round(stats.size / 1024)}KB`);
} else {
  console.log('   - ❌ Extension package missing');
}

console.log('\n🎯 Next Steps:');
console.log('1. Press F5 in VS Code to launch Extension Development Host');
console.log('2. In the new window, open test-debug.maxpat');
console.log('3. Check language mode shows "MaxPat" (bottom-right)');
console.log('4. Try Ctrl+Shift+P → "MaxPat: Validate Connections"');
console.log('5. Hover over "cycle~" to see documentation');

console.log('\n📋 Debugging checklist:');
console.log('- ✅ Extension compiled without errors');
console.log('- ✅ Extension packaged successfully');
console.log('- ✅ Extension installed in VS Code');
console.log('- ✅ Launch configuration fixed');
console.log('- ⏳ Ready for F5 debugging!');
