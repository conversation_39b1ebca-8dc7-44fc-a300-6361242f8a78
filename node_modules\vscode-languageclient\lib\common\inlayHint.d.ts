import { Disposable, TextDocument, ProviderResult, Range as VRange, InlayHint as VInlayHint, InlayHintsProvider, EventEmitter } from 'vscode';
import { ClientCapabilities, CancellationToken, ServerCapabilities, DocumentSelector, InlayHintOptions, InlayHintRegistrationOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient } from './features';
export declare type ProvideInlayHintsSignature = (this: void, document: TextDocument, viewPort: VRange, token: CancellationToken) => ProviderResult<VInlayHint[]>;
export declare type ResolveInlayHintSignature = (this: void, item: VInlayHint, token: CancellationToken) => ProviderResult<VInlayHint>;
export declare type InlayHintsMiddleware = {
    provideInlayHints?: (this: void, document: TextDocument, viewPort: VRange, token: CancellationToken, next: ProvideInlayHintsSignature) => ProviderResult<VInlayHint[]>;
    resolveInlayHint?: (this: void, item: VInlayHint, token: CancellationToken, next: ResolveInlayHintSignature) => ProviderResult<VInlayHint>;
};
export declare type InlayHintsProviderShape = {
    provider: InlayHintsProvider;
    onDidChangeInlayHints: EventEmitter<void>;
};
export declare class InlayHintsFeature extends TextDocumentLanguageFeature<boolean | InlayHintOptions, InlayHintRegistrationOptions, InlayHintsProviderShape, InlayHintsMiddleware> {
    constructor(client: FeatureClient<InlayHintsMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: InlayHintRegistrationOptions): [Disposable, InlayHintsProviderShape];
    private registerProvider;
}
