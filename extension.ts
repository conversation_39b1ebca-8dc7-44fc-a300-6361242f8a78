import * as vscode from 'vscode';
import { LanguageClient } from 'vscode-languageclient/node';

let client: LanguageClient;

export function activate(context: vscode.ExtensionContext) {
  // Create language client for MaxPat files
  client = new LanguageClient(
    'maxpatLanguageServer',
    'MaxPat Language Server',
    {
      // Server options - point to your Python/Node server
      // ...
    },
    {
      documentSelector: [{ scheme: 'file', language: 'maxpat' }],
      // Additional client options
    }
  );

  // Start the client
  client.start();
}

export function deactivate() {
  if (client) {
    return client.stop();
  }
}