{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../test/extension.test.ts"], "names": [], "mappings": ";;AAAA,iCAAiC;AACjC,iCAAiC;AAGjC,KAAK,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAC/C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;IAEzD,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACvC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,2BAA2B,EAAE,KAAK,IAAI,EAAE;QAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAClE,IAAI,SAAS,EAAE;YACb,MAAM,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC3B,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;SAC/B;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC3C,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAClD,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YAC9B,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,WAAW,GAAG;YAClB,SAAS,EAAE;gBACT,aAAa,EAAE,CAAC;gBAChB,OAAO,EAAE;oBACP;wBACE,KAAK,EAAE;4BACL,IAAI,EAAE,OAAO;4BACb,UAAU,EAAE,QAAQ;4BACpB,WAAW,EAAE,CAAC;4BACd,YAAY,EAAE,CAAC;4BACf,YAAY,EAAE,CAAC,QAAQ,CAAC;4BACxB,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;yBACpC;qBACF;oBACD;wBACE,KAAK,EAAE;4BACL,IAAI,EAAE,OAAO;4BACb,UAAU,EAAE,QAAQ;4BACpB,WAAW,EAAE,CAAC;4BACd,YAAY,EAAE,CAAC;4BACf,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;yBACpC;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP;wBACE,WAAW,EAAE;4BACX,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;4BACtB,aAAa,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;yBAC5B;qBACF;iBACF;aACF;SACF,CAAC;QAEF,uCAAuC;QACvC,oEAAoE;QACpE,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;QAClD,MAAM,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,aAAa,GAAG;YACpB,SAAS,EAAE;gBACT,aAAa,EAAE,CAAC;gBAChB,OAAO,EAAE;oBACP;wBACE,KAAK,EAAE;4BACL,IAAI,EAAE,OAAO;4BACb,UAAU,EAAE,QAAQ;4BACpB,WAAW,EAAE,CAAC;4BACd,YAAY,EAAE,CAAC;4BACf,YAAY,EAAE,CAAC,QAAQ,CAAC;4BACxB,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;yBACpC;qBACF;iBACF;gBACD,OAAO,EAAE;oBACP;wBACE,WAAW,EAAE;4BACX,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;4BACtB,aAAa,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,sBAAsB;yBACpD;qBACF;iBACF;aACF;SACF,CAAC;QAEF,qDAAqD;QACrD,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,iBAAiB;QACtF,MAAM,CAAC,EAAE,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,iBAAiB;IACpG,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAC5D,iEAAiE;QACjE,MAAM,YAAY,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QACrE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACzB,4DAA4D;YAC5D,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,4CAA4C,EAAE,GAAG,EAAE;QACtD,0DAA0D;QAC1D,MAAM,mBAAmB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC/E,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACvC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}