import { FileSystemWatcher as VFileSystemWatcher, WorkspaceFolder as VWorkspaceFolder } from 'vscode';
import { ClientCapabilities, ConfigurationRequest, DidChangeConfigurationRegistrationOptions, RegistrationType } from 'vscode-languageserver-protocol';
import { StaticFeature, FeatureClient, FeatureState, DynamicFeature, RegistrationData } from './features';
export interface ConfigurationMiddleware {
    configuration?: ConfigurationRequest.MiddlewareSignature;
}
interface ConfigurationWorkspaceMiddleware {
    workspace?: ConfigurationMiddleware;
}
/**
 * Configuration pull model. From server to client.
 */
export declare class ConfigurationFeature implements StaticFeature {
    private readonly _client;
    constructor(client: FeatureClient<ConfigurationWorkspaceMiddleware>);
    getState(): FeatureState;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(): void;
    private getConfiguration;
    dispose(): void;
}
export declare function toJSONObject(obj: any): any;
export interface DidChangeConfigurationSignature {
    (this: void, sections: string[] | undefined): Promise<void>;
}
export interface DidChangeConfigurationMiddleware {
    didChangeConfiguration?: (this: void, sections: string[] | undefined, next: DidChangeConfigurationSignature) => Promise<void>;
}
interface DidChangeConfigurationWorkspaceMiddleware {
    workspace?: DidChangeConfigurationMiddleware;
}
export declare type SynchronizeOptions = {
    /**
     * The configuration sections to synchronize. Pushing settings from the
     * client to the server is deprecated in favour of the new pull model
     * that allows servers to query settings scoped on resources. In this
     * model the client can only deliver an empty change event since the
     * actually setting value can vary on the provided resource scope.
     *
     * @deprecated Use the new pull model (`workspace/configuration` request)
     */
    configurationSection?: string | string[];
    /**
     * Asks the client to send file change events to the server. Watchers
     * operate on workspace folders. The LSP client doesn't support watching
     * files outside a workspace folder.
     */
    fileEvents?: VFileSystemWatcher | VFileSystemWatcher[];
};
export declare type $ConfigurationOptions = {
    synchronize?: SynchronizeOptions;
    workspaceFolder?: VWorkspaceFolder;
};
export declare class SyncConfigurationFeature implements DynamicFeature<DidChangeConfigurationRegistrationOptions> {
    private _client;
    private isDisposed;
    private readonly _listeners;
    constructor(_client: FeatureClient<DidChangeConfigurationWorkspaceMiddleware, $ConfigurationOptions>);
    getState(): FeatureState;
    get registrationType(): RegistrationType<DidChangeConfigurationRegistrationOptions>;
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(): void;
    register(data: RegistrationData<DidChangeConfigurationRegistrationOptions>): void;
    unregister(id: string): void;
    dispose(): void;
    private onDidChangeConfiguration;
    private extractSettingsInformation;
}
export {};
