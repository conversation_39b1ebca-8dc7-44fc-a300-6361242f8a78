import { Disposable, TextDocument, ProviderResult, Position as VPosition, CancellationToken, TypeHierarchyProvider as VTypeHierarchyProvider, TypeHierarchyItem as VTypeHierarchyItem } from 'vscode';
import { ClientCapabilities, DocumentSelector, ServerCapabilities, TypeHierarchyRegistrationOptions, TypeHierarchyOptions } from 'vscode-languageserver-protocol';
import { TextDocumentLanguageFeature, FeatureClient } from './features';
export declare type PrepareTypeHierarchySignature = (this: void, document: TextDocument, position: VPosition, token: CancellationToken) => ProviderResult<VTypeHierarchyItem[]>;
export declare type TypeHierarchySupertypesSignature = (this: void, item: VTypeHierarchyItem, token: CancellationToken) => ProviderResult<VTypeHierarchyItem[]>;
export declare type TypeHierarchySubtypesSignature = (this: void, item: VTypeHierarchyItem, token: CancellationToken) => ProviderResult<VTypeHierarchyItem[]>;
/**
 * Type hierarchy middleware
 *
 * @since 3.17.0
 */
export declare type TypeHierarchyMiddleware = {
    prepareTypeHierarchy?: (this: void, document: TextDocument, positions: VPosition, token: CancellationToken, next: PrepareTypeHierarchySignature) => ProviderResult<VTypeHierarchyItem[]>;
    provideTypeHierarchySupertypes?: (this: void, item: VTypeHierarchyItem, token: CancellationToken, next: TypeHierarchySupertypesSignature) => ProviderResult<VTypeHierarchyItem[]>;
    provideTypeHierarchySubtypes?: (this: void, item: VTypeHierarchyItem, token: CancellationToken, next: TypeHierarchySubtypesSignature) => ProviderResult<VTypeHierarchyItem[]>;
};
declare class TypeHierarchyProvider implements VTypeHierarchyProvider {
    private client;
    private middleware;
    constructor(client: FeatureClient<TypeHierarchyMiddleware>);
    prepareTypeHierarchy(document: TextDocument, position: VPosition, token: CancellationToken): ProviderResult<VTypeHierarchyItem[]>;
    provideTypeHierarchySupertypes(item: VTypeHierarchyItem, token: CancellationToken): ProviderResult<VTypeHierarchyItem[]>;
    provideTypeHierarchySubtypes(item: VTypeHierarchyItem, token: CancellationToken): ProviderResult<VTypeHierarchyItem[]>;
}
export declare class TypeHierarchyFeature extends TextDocumentLanguageFeature<boolean | TypeHierarchyOptions, TypeHierarchyRegistrationOptions, TypeHierarchyProvider, TypeHierarchyMiddleware> {
    constructor(client: FeatureClient<TypeHierarchyMiddleware>);
    fillClientCapabilities(capabilities: ClientCapabilities): void;
    initialize(capabilities: ServerCapabilities, documentSelector: DocumentSelector): void;
    protected registerLanguageProvider(options: TypeHierarchyRegistrationOptions): [Disposable, TypeHierarchyProvider];
}
export {};
